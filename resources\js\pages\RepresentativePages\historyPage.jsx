import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage, router } from '@inertiajs/react';
import { Eye, Filter, History, Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';
import UpdateModal from './partials/updateModal';

export default function HistoryPage() {
    const { historyData, totalStats, pagination } = usePage().props;
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('all');
    const [filterStatus, setFilterStatus] = useState('all');
    const [selectedEntry, setSelectedEntry] = useState(null);
    // const [modalMode, setModalMode] = useState('view');
    const [viewModal, setViewModal] = useState(null);
    const [editModal, setEditModal] = useState(null);

    // Pagination functions
    const handlePageChange = (page) => {
        if (page >= 1 && page <= pagination?.last_page && page !== pagination?.current_page) {
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            router.get(url.toString(), {}, { preserveState: true, preserveScroll: true });
        }
    };


    const filteredData = historyData?.filter((entry) => {
        const matchesSearch =
            entry.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entry.items.some((item) => item.description.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesType = filterType === 'all' || entry.type === filterType;
        const matchesStatus = filterStatus === 'all' || entry.status === filterStatus;

        return matchesSearch && matchesType && matchesStatus;
    });

    const getStatusColor = (status) => {
        switch (status) {
            case 'purchased':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'En attente':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'not_purchased':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getTypeColor = (type) => {
        switch (type) {
            case 'Or':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'Montre':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'Brocante':
                return 'bg-green-100 text-green-800 border-green-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getSatisfactionColor = (satisfaction) => {
        switch (satisfaction) {
            case 'Très satisfait':
                return 'text-green-600';
            case 'Satisfait':
                return 'text-blue-600';
            case 'Neutre':
                return 'text-yellow-600';
            case 'Insatisfait':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    const breadcrumbs = [
        {
            title: 'Historique',
            href: '/representative/history',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Historique" />
            <div className="space-y-6 p-3 lg:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-[#525e62]">Historique Post-RDV</h1>
                        <p className="text-[#525e62]/70">Consultez l'historique de vos saisies post-rendez-vous.</p>
                    </div>
                </div>

                {/* Statistiques rapides */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                    <Card className="border-0 bg-white shadow-lg">
                        <CardContent className="p-4">
                            <div className="text-center">
                                <p className="text-sm text-[#525e62]/70">Total Saisies</p>
                                <p className="text-2xl font-bold text-[#525e62]">{totalStats.totalEntries}</p>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="border-0 bg-white shadow-lg">
                        <CardContent className="p-4">
                            <div className="text-center">
                                <p className="text-sm text-[#525e62]/70">Total Rachats</p>
                                <p className="text-2xl font-bold text-[#525e62]">{totalStats.totalPurchase.toLocaleString()} €</p>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="border-0 bg-white shadow-lg">
                        <CardContent className="p-4">
                            <div className="text-center">
                                <p className="text-sm text-[#525e62]/70">Vente Total</p>
                                <p className="text-2xl font-bold text-green-600">{totalStats.totalBuy.toLocaleString()} €</p>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="border-0 bg-white shadow-lg">
                        <CardContent className="p-4">
                            <div className="text-center">
                                <p className="text-sm text-[#525e62]/70">Bénéfice Total</p>
                                <p className="text-2xl font-bold text-green-600">{totalStats.totalProfit.toLocaleString()} €</p>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="border-0 bg-white shadow-lg">
                        <CardContent className="p-4">
                            <div className="text-center">
                                <p className="text-sm text-[#525e62]/70">Marge Moyenne</p>
                                <p className="text-2xl font-bold text-blue-600">{totalStats.avgMargin.toFixed(1)}%</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filtres et recherche */}
                <Card className="border-0 bg-white shadow-lg">
                    <CardHeader>
                        <CardTitle className="flex items-center text-[#525e62]">
                            <Filter className="mr-2 h-5 w-5" />
                            Filtres et Recherche
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="relative">
                                <Search className="absolute top-3 left-3 h-4 w-4 text-[#525e62]/50" />
                                <Input
                                    placeholder="Rechercher client ou article..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Liste des entrées */}
                <Card className="border-0 bg-white shadow-lg">
                    <CardHeader>
                        <CardTitle className="flex items-center text-[#525e62]">
                            <History className="mr-2 h-5 w-5" />
                            Historique des Saisies ({filteredData.length})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredData.map((entry) => (
                                <div key={entry.id} className="rounded-lg border border-[#525e62]/10 p-4 transition-colors hover:bg-[#f1efe0]/30">
                                    <div className="mb-4 flex items-start justify-between">
                                        <div className="flex items-center space-x-4">
                                            <div className="text-center">
                                                <p className="text-sm text-[#525e62]/70">Date</p>
                                                <p className="font-medium text-[#525e62]">{entry.date}</p>
                                                <p className="text-xs text-[#525e62]/70">{entry.time}</p>
                                            </div>
                                            <div>
                                                <p className="text-lg font-bold text-[#525e62]">{entry.clientName}</p>
                                                <div className="mt-1 flex space-x-2">
                                                    <Badge className={getTypeColor(entry.type)}>{entry.type}</Badge>
                                                    <Badge className={getStatusColor(entry.status)}>
                                                        {entry.status === 'purchased' ? 'acheté' : 'non acheté'}
                                                    </Badge>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-3 text-right">
                                            {new Date(entry.date) <= new Date(new Date().toISOString().slice(0, 10)) && (
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => {
                                                        setSelectedEntry(entry);
                                                        setEditModal(true);
                                                    }}
                                                    className="border-[#525e62] text-[#525e62]"
                                                >
                                                    Modifier
                                                </Button>
                                            )}
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => {
                                                    setSelectedEntry(entry);
                                                    setViewModal(true);
                                                }}
                                                className="border-[#525e62] text-[#525e62]"
                                            >
                                                <Eye className="mr-2 h-4 w-4" />
                                                Détails
                                            </Button>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-4">
                                        <div className="rounded bg-blue-50 p-3">
                                            <p className="text-[#525e62]/70">Prix de rachat</p>
                                            <p className="font-bold text-[#525e62]">{entry.totalPurchase.toLocaleString()} €</p>
                                        </div>
                                        <div className="rounded bg-green-50 p-3">
                                            <p className="text-[#525e62]/70">Valeur estimée</p>
                                            <p className="font-bold text-[#525e62]">{entry.totalEstimated.toLocaleString()} €</p>
                                        </div>
                                        <div className="rounded bg-yellow-50 p-3">
                                            <p className="text-[#525e62]/70">Bénéfice</p>
                                            <p className="font-bold text-green-600">+{entry.profit.toLocaleString()} €</p>
                                        </div>
                                    </div>

                                    <div className="mt-3 border-t border-[#525e62]/10 pt-3">
                                        <p className="mb-2 text-sm text-[#525e62]/70">Articles rachetés:</p>
                                        <div className="space-y-1">
                                            {entry.items.map((item, index) => (
                                                <p key={index} className="text-sm text-[#525e62]">
                                                    • {item.description}
                                                    {item.weight > 0 && ` (${item.weight}g)`}- {item.purchasePrice.toLocaleString()} €
                                                </p>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Pagination */}
                {pagination && pagination.last_page > 1 && (
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-4 bg-white rounded-lg border border-[#525e62]/10">
                        {/* Results info */}
                        <div className="text-sm text-[#525e62]/70">
                            Affichage de <span className="font-medium">{pagination.from}</span> à{' '}
                            <span className="font-medium">{pagination.to}</span> sur{' '}
                            <span className="font-medium">{pagination.total}</span> résultats
                        </div>

                        {/* Pagination controls */}
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePageChange(pagination.current_page - 1)}
                                disabled={pagination.current_page <= 1}
                                className="h-8 px-3"
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" />
                                Précédent
                            </Button>

                            <span className="text-sm text-[#525e62] px-3">
                                Page {pagination.current_page} sur {pagination.last_page}
                            </span>

                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePageChange(pagination.current_page + 1)}
                                disabled={pagination.current_page >= pagination.last_page}
                                className="h-8 px-3"
                            >
                                Suivant
                                <ChevronRight className="h-4 w-4 ml-1" />
                            </Button>
                        </div>
                    </div>
                )}

                {/* Modal de détails */}
                {viewModal && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                        <Card className="max-h-[70vh] w-full max-w-2xl overflow-y-auto bg-white">
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between text-[#525e62]">
                                    Détails - {selectedEntry.clientName}
                                    <Button variant="ghost" size="icon" onClick={() => setViewModal(null)}>
                                        ×
                                    </Button>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-sm text-[#525e62]/70">Date et heure</p>
                                            <p className="font-medium text-[#525e62]">
                                                {selectedEntry.date} à {selectedEntry.time}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-[#525e62]/70">Type</p>
                                            <div className={`rounded p-3 ${getTypeColor(selectedEntry.type)}`}>{selectedEntry.type}</div>
                                        </div>
                                    </div>

                                    <div className="space-y-3">
                                        <h4 className="font-medium text-[#525e62]">Articles rachetés</h4>
                                        {selectedEntry.items.map((item, index) => (
                                            <div key={index} className="rounded-lg bg-[#f1efe0]/30 p-3">
                                                <p className="font-medium text-[#525e62]">{item.description}</p>
                                                <div className="mt-2 grid grid-cols-3 gap-2 text-sm">
                                                    {item.weight > 0 && (
                                                        <div>
                                                            <span className="text-[#525e62]/70">Poids: </span>
                                                            <span className="text-[#525e62]">{item.weight}g</span>
                                                        </div>
                                                    )}
                                                    <div>
                                                        <span className="text-[#525e62]/70">Rachat: </span>
                                                        <span className="text-[#525e62]">{item.purchasePrice.toLocaleString()} €</span>
                                                    </div>
                                                    <div>
                                                        <span className="text-[#525e62]/70">Estimé: </span>
                                                        <span className="text-[#525e62]">{item.estimatedValue.toLocaleString()} €</span>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 border-t border-[#525e62]/10 pt-4">
                                        <div>
                                            <p className="text-sm text-[#525e62]/70">Marge bénéficiaire</p>
                                            <p className="text-xl font-bold text-green-600">{selectedEntry.margin.toFixed(1)}%</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
            <UpdateModal open={editModal} selectedEntry={selectedEntry} setEditModal={setEditModal} getTypeColor={getTypeColor} />
        </AppLayout>
    );
}
