<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'is_activated'
    ];
    // If the user is a Sales Assistant:
    public function createdAppointments()
    {
        return $this->hasMany(Appointment::class, 'assistant_id');
    }

    // If the user is a Sales Representative:
    public function receivedAppointments()
    {
        return $this->hasMany(Appointment::class, 'representative_id');
    }

    // General appointments relationship (for both assistants and representatives)
    public function appointments()
    {
        // This will return appointments where the user is either assistant or representative
        return $this->hasMany(Appointment::class, 'assistant_id');
    }

    public function bonuses()
    {
        return $this->hasMany(Bonus::class);
    }

    // For assistants: get assigned representatives
    public function assignedRepresentatives()
    {
        return $this->belongsToMany(User::class, 'assistant_representative_assignments', 'assistant_id', 'representative_id');
    }

    // For representatives: get assigned assistants
    public function assignedAssistants()
    {
        return $this->belongsToMany(User::class, 'assistant_representative_assignments', 'representative_id', 'assistant_id');
    }

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_activated' => 'boolean',
        ];
    }
}
