import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { Activity, Award, BarChart3, Calendar, Crown, Search, Target, Users } from 'lucide-react';
import { useState } from 'react';

export default function Pairings() {
    const { pairingInsights, pairingAnalysis } = usePage().props;
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('success_rate');

    // Use real data from props
    const allPairings = pairingInsights?.all_pairings || [];

    const filteredPairings = allPairings
        .filter((pairing) => {
            const matchesSearch =
                pairing.assistant_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                pairing.representative_name.toLowerCase().includes(searchTerm.toLowerCase());
            return matchesSearch;
        })
        .sort((a, b) => {
            switch (sortBy) {
                case 'success_rate':
                    return b.success_rate - a.success_rate;
                case 'collaboration':
                    return b.collaboration_score - a.collaboration_score;
                case 'appointments':
                    return b.total_appointments - a.total_appointments;
                default:
                    return 0;
            }
        });

    const PairingCard = ({ pairing, isTopPair = false }) => (
        <Card className={`border-[#525e62]/10 ${isTopPair ? 'bg-gradient-to-r from-[#f1efe0] to-white ring-2 ring-yellow-400' : ''}`}>
            <CardContent className="p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        {isTopPair && <Crown className="h-5 w-5 text-yellow-500" />}
                        <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-2">
                                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#525e62] font-medium text-white">
                                    {pairing.assistant_avatar}
                                </div>
                                <div>
                                    <p className="font-medium text-[#525e62]">{pairing.assistant_name}</p>
                                    <p className="text-sm text-[#525e62]/70">Assistant</p>
                                </div>
                            </div>
                            <div className="text-2xl text-[#525e62]">+</div>
                            <div className="flex items-center space-x-2">
                                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#8b9dc3] font-medium text-white">
                                    {pairing.representative_avatar}
                                </div>
                                <div>
                                    <p className="font-medium text-[#525e62]">{pairing.representative_name}</p>
                                    <p className="text-sm text-[#525e62]/70">Représentant</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center space-x-6">
                        {/* <div className="text-center">
                            <p className="text-sm text-[#525e62]/70">Source</p>
                            <Badge variant="outline" className="capitalize">
                                {pairing.source}
                            </Badge>
                        </div> */}
                        <div className="text-center">
                            <p className="text-sm text-[#525e62]/70">RDV Total</p>
                            <p className="text-lg font-bold text-[#525e62]">{pairing.total_appointments}</p>
                            <p className="text-xs text-[#525e62]/50">appointments</p>
                        </div>
                        <div className="text-center">
                            <p className="text-sm text-[#525e62]/70">Taux de réussite</p>
                            <p className="text-lg font-bold text-[#525e62]">{pairing.success_rate}%</p>
                            <p className="text-xs text-[#525e62]/50">
                                {pairing.successful_appointments}/{pairing.total_appointments} réussis
                            </p>
                        </div>
                        {/* <div className="text-center">
                            <p className="text-sm text-[#525e62]/70">Collaboration</p>
                            <div className="flex items-center">
                                <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                    <div
                                        className="bg-[#525e62] h-2 rounded-full"
                                        style={{ width: `${pairing.collaboration_score}%` }}
                                    ></div>
                                </div>
                                <span className="text-sm font-medium text-[#525e62]">
                                    {pairing.collaboration_score}%
                                </span>
                            </div>
                        </div> */}
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout>
            <Head title="Analyse des Binômes" />

            <div className="min-h-screen bg-white p-6">
                <div className="mx-auto max-w-7xl space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-[#525e62]">Analyse des Binômes</h1>
                            <p className="text-[#525e62]/70">Évaluation des performances des équipes Assistant-Représentant</p>
                        </div>
                    </div>

                    {/* Summary Stats */}
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                        <Card className="border-[#525e62]/10">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">Binômes Actifs</p>
                                        <p className="text-2xl font-bold text-[#525e62]">{pairingInsights?.total_pairings || 0}</p>
                                    </div>
                                    <Users className="h-8 w-8 text-[#525e62]/50" />
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="border-[#525e62]/10">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">Taux Moyen</p>
                                        <p className="text-2xl font-bold text-[#525e62]">{(pairingInsights?.avg_success_rate || 0).toFixed(1)}%</p>
                                    </div>
                                    <Target className="h-8 w-8 text-[#525e62]/50" />
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="border-[#525e62]/10">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">RDV Total</p>
                                        <p className="text-2xl font-bold text-[#525e62]">
                                            {filteredPairings.reduce((sum, p) => sum + (p.total_appointments || 0), 0)}
                                        </p>
                                    </div>
                                    <Calendar className="h-8 w-8 text-[#525e62]/50" />
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="border-[#525e62]/10">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-[#525e62]/70">Score Collaboration</p>
                                        <p className="text-2xl font-bold text-[#525e62]">
                                            {(pairingInsights?.avg_collaboration_score || 0).toFixed(1)}%
                                        </p>
                                    </div>
                                    <Award className="h-8 w-8 text-[#525e62]/50" />
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Performance Analysis */}
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                        <Card className="border-[#525e62]/10">
                            <CardHeader>
                                <CardTitle className="flex items-center text-[#525e62]">
                                    <BarChart3 className="mr-2 h-5 w-5" />
                                    Analyse des Sources
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between rounded-lg bg-[#f1efe0]/30 p-3">
                                        <div>
                                            <p className="font-medium text-[#525e62]">Source la plus performante</p>
                                            <p className="text-sm text-[#525e62]/70">{pairingAnalysis?.best_performing_source || 'N/A'}</p>
                                        </div>
                                        <Badge className="bg-green-600">Top</Badge>
                                    </div>
                                    <div className="flex items-center justify-between rounded-lg bg-[#f1efe0]/30 p-3">
                                        <div>
                                            <p className="font-medium text-[#525e62]">Binômes performants</p>
                                            <p className="text-sm text-[#525e62]/70">{pairingAnalysis?.high_performers || 0} binômes (≥80%)</p>
                                        </div>
                                        <Award className="h-5 w-5 text-[#525e62]" />
                                    </div>
                                    <div className="flex items-center justify-between rounded-lg bg-[#f1efe0]/30 p-3">
                                        <div>
                                            <p className="font-medium text-[#525e62]">Opportunités d'amélioration</p>
                                            <p className="text-sm text-[#525e62]/70">
                                                {pairingAnalysis?.improvement_opportunities || 0} binômes (&lt;70%)
                                            </p>
                                        </div>
                                        <Target className="h-5 w-5 text-[#525e62]" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-[#525e62]/10">
                            <CardHeader>
                                <CardTitle className="flex items-center text-[#525e62]">
                                    <Activity className="mr-2 h-5 w-5" />
                                    Métriques Globales
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="rounded-lg bg-[#f1efe0]/30 p-4 text-center">
                                        <p className="text-2xl font-bold text-[#525e62]">{pairingAnalysis?.avg_success_rate || 0}%</p>
                                        <p className="text-sm text-[#525e62]/70">Taux de réussite moyen</p>
                                    </div>
                                    <div className="rounded-lg bg-[#f1efe0]/30 p-4 text-center">
                                        <p className="text-2xl font-bold text-[#525e62]">{pairingAnalysis?.avg_collaboration_score || 0}%</p>
                                        <p className="text-sm text-[#525e62]/70">Score collaboration moyen</p>
                                    </div>
                                    <div className="rounded-lg bg-[#f1efe0]/30 p-4 text-center">
                                        <p className="text-2xl font-bold text-[#525e62]">{pairingAnalysis?.total_active_pairings || 0}</p>
                                        <p className="text-sm text-[#525e62]/70">Binômes actifs cette semaine</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                    {/* Top Pair Highlight */}
                    {pairingInsights?.top_pair && (
                        <div>
                            <h2 className="mb-4 flex items-center text-xl font-semibold text-[#525e62]">
                                <Crown className="mr-2 h-5 w-5 text-yellow-500" />
                                Meilleur Binôme de la Semaine
                            </h2>
                            <PairingCard pairing={pairingInsights.top_pair} isTopPair={true} />
                        </div>
                    )}
                    {/* Search */}
                    <Card className="border-[#525e62]/10">
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                                <div className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-[#525e62]/50" />
                                        <Input
                                            placeholder="Rechercher par nom d'assistant ou représentant..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* All Pairings */}
                    <div>
                        <h2 className="mb-4 text-xl font-semibold text-[#525e62]">Tous les Binômes ({filteredPairings.length})</h2>
                        <div className="space-y-4">
                            {filteredPairings.length > 0 ? (
                                filteredPairings
                                    .slice(pairingInsights?.top_pair ? 1 : 0)
                                    .map((pairing, index) => (
                                        <PairingCard key={`${pairing.assistant_id}-${pairing.representative_id}`} pairing={pairing} />
                                    ))
                            ) : (
                                <Card className="border-[#525e62]/10">
                                    <CardContent className="p-6 text-center">
                                        <p className="text-[#525e62]/70">Aucun binôme trouvé pour cette période</p>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
