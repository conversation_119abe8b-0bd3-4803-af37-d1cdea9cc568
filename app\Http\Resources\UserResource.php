<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
            'is_activated' => $this->is_activated,
            'email_verified_at' => $this->email_verified_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Conditional relationships
            'created_appointments_count' => $this->whenCounted('createdAppointments'),
            'received_appointments_count' => $this->whenCounted('receivedAppointments'),
            'bonuses_count' => $this->whenCounted('bonuses'),
            
            // Load relationships when requested
            'created_appointments' => AppointmentResource::collection($this->whenLoaded('createdAppointments')),
            'received_appointments' => AppointmentResource::collection($this->whenLoaded('receivedAppointments')),
            'bonuses' => BonusResource::collection($this->whenLoaded('bonuses')),
            'assigned_representatives' => UserResource::collection($this->whenLoaded('assignedRepresentatives')),
            'assigned_assistants' => UserResource::collection($this->whenLoaded('assignedAssistants')),
        ];
    }
}
