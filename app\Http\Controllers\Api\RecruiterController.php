<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\Purchase;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RecruiterController extends Controller
{
    public function getDashboardStats(Request $request)
    {
        $now = Carbon::now();
        $weekStart = $now->copy()->startOfWeek()->format('Y-m-d H:i:s');
        $weekEnd = $now->copy()->endOfWeek()->format('Y-m-d H:i:s');

        // Get assistants performance
        $assistantsData = $this->getAssistantsPerformance($weekStart, $weekEnd);
        
        // Get representatives performance
        $representativesData = $this->getRepresentativesPerformance($weekStart, $weekEnd);
        
        // Get pairing insights
        $pairingInsights = $this->getPairingInsights($weekStart, $weekEnd);

        return response()->json([
            'success' => true,
            'data' => [
                'assistants' => $assistantsData,
                'representatives' => $representativesData,
                'pairings' => $pairingInsights,
                'week_period' => [
                    'start' => Carbon::parse($weekStart)->format('d M'),
                    'end' => Carbon::parse($weekEnd)->format('d M Y')
                ]
            ]
        ]);
    }

    private function getAssistantsPerformance($weekStart, $weekEnd)
    {
        $assistants = User::where('role', 'assistant')
            ->get()
            ->map(function ($assistant) use ($weekStart, $weekEnd) {
                $totalAppointments = Appointment::where('assistant_id', $assistant->id)
                    ->whereBetween('created_at', [$weekStart, $weekEnd])
                    ->count();

                $validatedAppointments = Appointment::where('assistant_id', $assistant->id)
                    ->whereBetween('created_at', [$weekStart, $weekEnd])
                    ->whereHas('purchases', function ($query) {
                        $query->where('status', 'purchased');
                    })
                    ->count();

                $validationRate = $totalAppointments > 0 
                    ? round(($validatedAppointments / $totalAppointments) * 100, 1) 
                    : 0;

                return [
                    'id' => $assistant->id,
                    'name' => $assistant->name,
                    'email' => $assistant->email,
                    'total_appointments' => $totalAppointments,
                    'validated_appointments' => $validatedAppointments,
                    'validation_rate' => $validationRate,
                    'avatar' => $this->generateInitials($assistant->name),
                ];
            })
            ->sortByDesc('validated_appointments')
            ->values();

        return [
            'total_assistants' => $assistants->count(),
            'total_appointments' => $assistants->sum('total_appointments'),
            'total_validated' => $assistants->sum('validated_appointments'),
            'avg_validation_rate' => $assistants->avg('validation_rate'),
            'leaderboard' => $assistants->take(5),
            'all_assistants' => $assistants,
        ];
    }

    private function generateInitials($name)
    {
        $words = explode(' ', trim($name));
        return strtoupper(substr($words[0], 0, 1) . (isset($words[1]) ? substr($words[1], 0, 1) : ''));
    }
}