<?php

use App\Http\Controllers\ExecutiveController;
use Illuminate\Support\Facades\Route;



Route::middleware(['auth', 'verified', 'activated', 'role:executive'])->group(function () {

    // ? Pages

    Route::get("/welcomeExecutive", [ExecutiveController::class, "welcomeExecutive"]);
    Route::get("/payrollPage", [ExecutiveController::class, "payrollPage"]);
        Route::get('/bonuses/export', [ExecutiveController::class, 'exportBonuses'])->name('admin.bonuses.export');

    // ? Functions

});
