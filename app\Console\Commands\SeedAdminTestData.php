<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\AdminTestDataSeeder;

class SeedAdminTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:seed-test-data {--fresh : Fresh migration before seeding}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed the database with fake data for testing admin pages';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('fresh')) {
            $this->info('🔄 Running fresh migrations...');
            $this->call('migrate:fresh');
        }

        $this->info('🌱 Seeding admin test data...');

        $seeder = new AdminTestDataSeeder();
        $seeder->setCommand($this);
        $seeder->run();

        $this->newLine();
        $this->info('🎉 Admin test data seeded successfully!');
        $this->info('🚀 You can now test the admin pages with realistic data.');
        $this->newLine();
        $this->info('💡 Access the admin dashboard at: /admin/dashboard');
        $this->info('🔑 Login with: <EMAIL> / password');
    }
}
