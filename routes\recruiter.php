<?php

use App\Http\Controllers\RecruiterController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified', 'activated', 'role:recruiter'])->group(function () {
    Route::get('recruiter/dashboard', [RecruiterController::class, 'dashboard'])->name('recruiter.dashboard');
    Route::get('recruiter/performance', [RecruiterController::class, 'performance'])->name('recruiter.performance');
    Route::get('recruiter/pairings', [RecruiterController::class, 'pairings'])->name('recruiter.pairings');
});
