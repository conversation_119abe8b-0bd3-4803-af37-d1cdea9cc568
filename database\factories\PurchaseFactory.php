<?php

namespace Database\Factories;

use App\Models\Purchase;
use App\Models\Appointment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Purchase>
 */
class PurchaseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $buyPrice = $this->faker->randomFloat(2, 10, 500);
        $resalePrice = $buyPrice * $this->faker->randomFloat(2, 1.2, 3.0); // 20% to 200% markup
        $benefit = $resalePrice - $buyPrice;

        return [
            'appointment_id' => Appointment::factory(),
            'status' => $this->faker->randomElement(['purchased', 'not_purchased']),
            'description' => $this->faker->optional()->sentence(),
            'item_type' => $this->faker->randomElement(['bijoux', 'or', 'argent', 'montres', 'objets de valeur']),
            'weight' => $this->faker->optional()->randomFloat(2, 1, 100),
            'buy_price' => $buyPrice,
            'resale_price' => $resalePrice,
            'benefit' => $benefit,
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the purchase was successful.
     */
    public function purchased(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'purchased',
        ]);
    }

    /**
     * Indicate that the purchase was not successful.
     */
    public function notPurchased(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'not_purchased',
            'buy_price' => 0,
            'resale_price' => 0,
            'benefit' => 0,
        ]);
    }
}
