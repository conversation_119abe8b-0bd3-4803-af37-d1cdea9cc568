"use client"

import { Card, CardContent } from "@/components/ui/card"

export function StatsCard({ title, value, icon: Icon, color }) {
    return (
        <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-shadow duration-200">
            <CardContent className="p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="text-sm font-medium text-[#525e62]/70">{title}</p>
                        <p className="text-2xl font-bold text-[#525e62] mt-1">{value}</p>
                    </div>
                    <div className={`p-3 rounded-full ${color}`}>
                        <Icon className="h-6 w-6 text-white" />
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
