<?php

namespace App\Exports;

use App\Models\Appointment;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AppointmentsExport implements FromQuery, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    public function __construct(public $month, public $year)
    {
        $this->month = $month;
        $this->year = $year;
    }
    public function query()
    {
        return Appointment::query()
            ->leftJoin('users as assistants', 'appointments.assistant_id', '=', 'assistants.id')
            ->leftJoin('users as representatives', 'appointments.representative_id', '=', 'representatives.id')
            ->select([
                'assistants.name as assistant_name',
                'representatives.name as representative_name',
                'appointments.clientName',
                'appointments.clientPhone',
                'appointments.clientAddress',
                'appointments.source',
                'appointments.dateTime',
                'appointments.notes',
                'appointments.itemsCollection',
                'appointments.appointment_type',
            ])
            ->whereYear('appointments.dateTime', $this->year)
            ->whereMonth('appointments.dateTime', $this->month);
    }
    public function headings(): array
    {
        return [
            'Assistant',
            'Representative',
            'Nom du client',
            'Telephone du client',
            'Adresse du client',
            'Source',
            'Date',
            'Notes',
            'Collection d\'articles',
            'Type de rendez-vous',
        ];
    }
     public function map($row): array
    {
        // Translate source
        $source = $row->source === 'outbound' ? 'sortant' : $row->source;

        
        $appointmentType = $row->appointment_type === 'announced' ? 'annoncé' : 'non annoncé' ;

        return [
            $row->assistant_name,
            $row->representative_name,
            $row->clientName,
            $row->clientPhone,
            $row->clientAddress,
            $source,
            $row->dateTime,
            $row->notes,
            $row->itemsCollection,
            $appointmentType,
        ];
    }
    public function styles(Worksheet $sheet)
    {
        // Color the heading row (A1:J1) with a light blue background
        $sheet->getStyle('A1:J1')->applyFromArray([
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9E1F2'],
            ],
            'font' => [
                'bold' => true,
            ],
        ]);
    }
}
