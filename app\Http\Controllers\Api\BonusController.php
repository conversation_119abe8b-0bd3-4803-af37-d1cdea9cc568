<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\BonusResource;
use App\Http\Traits\ApiResponse;
use App\Models\Bonus;
use App\Models\User;
use App\Models\Appointment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BonusController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of bonuses
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $userId = $request->get('user_id');
        $appointmentId = $request->get('appointment_id');
        $type = $request->get('type');

        $query = Bonus::with(['user', 'appointment']);

        // Filter by user
        if ($userId) {
            $query->where('user_id', $userId);
        }

        // Filter by appointment
        if ($appointmentId) {
            $query->where('appointment_id', $appointmentId);
        }

        // Filter by type
        if ($type) {
            $query->where('type', $type);
        }

        // Order by creation date
        $query->orderBy('created_at', 'desc');

        $bonuses = $query->paginate($perPage);

        return $this->paginatedResponse(
            BonusResource::collection($bonuses),
            'Bonuses retrieved successfully'
        );
    }

    /**
     * Store a newly created bonus
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'amount' => 'required|numeric',
            'type' => 'required|string',
            'reason' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $bonus = Bonus::create($request->all());
        $bonus->load(['user', 'appointment']);

        return $this->createdResponse(
            new BonusResource($bonus),
            'Bonus created successfully'
        );
    }

    /**
     * Display the specified bonus
     */
    public function show(Bonus $bonus): JsonResponse
    {
        $bonus->load(['user', 'appointment']);

        return $this->successResponse(
            new BonusResource($bonus),
            'Bonus retrieved successfully'
        );
    }

    /**
     * Update the specified bonus
     */
    public function update(Request $request, Bonus $bonus): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'sometimes|required|exists:users,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'amount' => 'sometimes|required|numeric',
            'type' => 'sometimes|required|string',
            'reason' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $bonus->update($request->all());
        $bonus->load(['user', 'appointment']);

        return $this->successResponse(
            new BonusResource($bonus),
            'Bonus updated successfully'
        );
    }

    /**
     * Remove the specified bonus
     */
    public function destroy(Bonus $bonus): JsonResponse
    {
        $bonus->delete();

        return $this->successResponse(null, 'Bonus deleted successfully');
    }

    /**
     * Get bonuses for a specific user
     */
    public function getByUser(User $user): JsonResponse
    {
        $bonuses = $user->bonuses()->with(['user', 'appointment'])->get();

        return $this->successResponse(
            BonusResource::collection($bonuses),
            'User bonuses retrieved successfully'
        );
    }

    /**
     * Get bonuses for a specific appointment
     */
    public function getByAppointment(Appointment $appointment): JsonResponse
    {
        $bonuses = $appointment->bonuses()->with(['user', 'appointment'])->get();

        return $this->successResponse(
            BonusResource::collection($bonuses),
            'Appointment bonuses retrieved successfully'
        );
    }

    /**
     * Get bonus statistics
     */
    public function getStats(Request $request): JsonResponse
    {
        $userId = $request->get('user_id');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $query = Bonus::query();

        if ($userId) {
            $query->where('user_id', $userId);
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }
        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        $stats = [
            'total_bonuses' => $query->count(),
            'total_amount' => $query->sum('amount'),
            'average_amount' => $query->avg('amount'),
            'positive_bonuses' => $query->where('amount', '>', 0)->count(),
            'negative_bonuses' => $query->where('amount', '<', 0)->count(),
        ];

        return $this->successResponse($stats, 'Bonus statistics retrieved successfully');
    }

    /**
     * Get current user's bonuses
     */
    public function myBonuses(Request $request): JsonResponse
    {
        $user = $request->user();
        $perPage = $request->get('per_page', 15);

        $bonuses = $user->bonuses()
            ->with(['user', 'appointment'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return $this->paginatedResponse(
            BonusResource::collection($bonuses),
            'User bonuses retrieved successfully'
        );
    }
}
