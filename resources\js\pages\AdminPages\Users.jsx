import { useState, useEffect, useRef } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Users, 
    UserPlus, 
    Edit,
    Trash2,
    Search,
    Filter,
    Shield,
    Key,
    UserCheck,
    UserX,
    Mail,
    Calendar
} from 'lucide-react';

const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Gestion utilisateurs',
        href: '/admin/users',
    },
];

export default function AdminUsers() {
    const { users, userStats, filters, errors, flash } = usePage().props;
    
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showPasswordModal, setShowPasswordModal] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [selectedRole, setSelectedRole] = useState(filters?.role || '');
    const [showFilters, setShowFilters] = useState(false);
    const [isToggling, setIsToggling] = useState(false);

    // Form states
    const [newUser, setNewUser] = useState({
        name: '',
        email: '',
        role: 'assistant'
    });
    
    const [editUser, setEditUser] = useState({
        name: '',
        email: '',
        role: ''
    });

    // Keep the create modal open if there are validation errors
    useEffect(() => {
        if (errors && Object.keys(errors).length > 0) {
            setShowCreateModal(true);
        }
    }, [errors]);

    // Handle keyboard navigation for confirmation modal
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Escape' && showConfirmModal) {
                setShowConfirmModal(false);
                setSelectedUser(null);
            }
        };

        if (showConfirmModal) {
            document.addEventListener('keydown', handleKeyDown);
            // Focus management for accessibility
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [showConfirmModal]);


    const handleSearch = () => {
        router.get('/admin/users', {
            search: searchTerm,
            role: selectedRole,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedRole('');
        router.get('/admin/users');
    };

    const handleCreateUser = () => {
        router.post('/admin/users', newUser, {
            onSuccess: () => {
                setShowCreateModal(false);
                setNewUser({ name: '', email: '', role: 'assistant' });
            },
            onError: (errors) => {
                // Keep the modal open when there are validation errors
                // The errors will be displayed below the inputs
                console.log('Validation errors:', errors);
            },
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleEditUser = () => {
        // Only send name and email for profile updates
        const profileData = {
            name: editUser.name,
            email: editUser.email
        };

        router.put(`/admin/users/${selectedUser.id}`, profileData, {
            onSuccess: () => {
                setShowEditModal(false);
                setSelectedUser(null);
                setEditUser({ name: '', email: '', role: '' });
            },
            onError: (errors) => {
                console.error('Error updating user profile:', errors);
            }
        });
    };

    const handleResetPassword = () => {
        router.post(`/admin/users/${selectedUser.id}/reset-password`, {}, {
            onSuccess: (response) => {
                setShowPasswordModal(false);
                setSelectedUser(null);
                // Show success message
                alert('Email de réinitialisation envoyé avec succès !');
            },
            onError: (errors) => {
                console.error('Error sending password reset email:', errors);
                alert('Erreur lors de l\'envoi de l\'email de réinitialisation');
            }
        });
    };

    const handleToggleUserStatus = (user) => {
        setSelectedUser(user);
        setShowConfirmModal(true);
    };

    const confirmToggleUserStatus = () => {
        if (!selectedUser) return;

        setIsToggling(true);
        router.post(`/admin/users/${selectedUser.id}/toggle-status`, {}, {
            onSuccess: () => {
                setShowConfirmModal(false);
                setSelectedUser(null);
                setIsToggling(false);
            },
            onError: (errors) => {
                
                alert('Erreur lors de la modification du statut utilisateur');
                setIsToggling(false);
            }
        });
    };

    const openEditModal = (user) => {
        setSelectedUser(user);
        setEditUser({
            name: user.name,
            email: user.email,
            role: user.role
        });
        setShowEditModal(true);
    };

    const openPasswordModal = (user) => {
        setSelectedUser(user);
        setShowPasswordModal(true);
    };

    const getRoleBadge = (role) => {
        const roleConfig = {
            'assistant': { label: 'Assistant', className: 'bg-blue-50 text-blue-700' },
            'representative': { label: 'Représentant', className: 'bg-green-50 text-green-700' },
            'recruiter': { label: 'Recruteur', className: 'bg-purple-50 text-purple-700' },
            'executive': { label: 'Exécutif', className: 'bg-orange-50 text-orange-700' },
            'admin': { label: 'Admin', className: 'bg-red-50 text-red-700' },
        };
        
        const config = roleConfig[role] || { label: role, className: 'bg-gray-50 text-gray-700' };
        
        return (
            <Badge variant="outline" className={config.className}>
                {config.label}
            </Badge>
        );
    };

    const StatCard = ({ title, value, subtitle, icon: Icon, color = "text-blue-600" }) => (
        <Card className="h-full border-0 bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-3 sm:p-4">
                <div className="flex items-center justify-between">
                    <div className="min-w-0 flex-1">
                        <p className="text-xs font-medium text-gray-600 truncate">{title}</p>
                        <p className={`text-lg sm:text-xl font-bold ${color} mt-1`}>{value}</p>
                        {subtitle && <p className="text-xs text-gray-500 mt-1 truncate">{subtitle}</p>}
                    </div>
                    <div className="p-2 rounded-full bg-gray-50 flex-shrink-0 ml-2">
                        <Icon className={`h-4 w-4 sm:h-5 sm:w-5 ${color}`} />
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const UserCard = ({ user }) => (
        <Card className={`border-0 shadow-sm hover:shadow-md transition-shadow ${!user.is_activated ? 'bg-gray-50 opacity-75' : 'bg-white'}`}>
            <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col sm:flex-row justify-between items-start mb-4 space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                        <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center flex-shrink-0 ${!user.is_activated ? 'bg-gray-200' : 'bg-blue-100'}`}>
                            <Users className={`h-5 w-5 sm:h-6 sm:w-6 ${!user.is_activated ? 'text-gray-500' : 'text-blue-600'}`} />
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className={`font-semibold text-sm sm:text-base truncate ${!user.is_activated ? 'text-gray-600' : 'text-gray-900'}`}>{user.name}</h3>
                            <p className={`text-xs sm:text-sm flex items-center mt-1 ${!user.is_activated ? 'text-gray-400' : 'text-gray-500'}`}>
                                <Mail className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                                <span className="truncate">{user.email}</span>
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2 flex-shrink-0">
                        {getRoleBadge(user.role)}
                        {!user.is_activated && (
                            <Badge variant="secondary" className="bg-red-100 text-red-700 text-xs">
                                Désactivé
                            </Badge>
                        )}
                    </div>
                </div>

                <div className={`flex items-center text-xs sm:text-sm mb-4 ${!user.is_activated ? 'text-gray-500' : 'text-gray-600'}`}>
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                    <span>Créé le {new Date(user.created_at).toLocaleDateString('fr-FR')}</span>
                </div>

                <div className="flex flex-col justify-end space-y-2">
                    <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openEditModal(user)}
                        className="w-full sm:w-auto text-xs sm:text-sm"
                    >
                        <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                        Modifier profil
                    </Button>
                    <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openPasswordModal(user)}
                        className="w-full sm:w-auto text-xs sm:text-sm"
                    >
                        <Key className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                        Reset mot de passe
                    </Button>
                    <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleToggleUserStatus(user)}
                        className={`w-full sm:w-auto text-xs sm:text-sm ${
                            user.is_activated
                                ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                                : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                        }`}
                    >
                        {user.is_activated ? (
                            <>
                                <UserX className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                Désactiver
                            </>
                        ) : (
                            <>
                                <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                Activer
                            </>
                        )}
                    </Button>
                </div>
            </CardContent>
        </Card>
    );





    const PasswordModal = () => (
        showPasswordModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 w-full max-w-md">
                    <h3 className="text-lg font-semibold mb-4">
                        Réinitialiser le mot de passe
                    </h3>

                    <div className="space-y-4">
                        <div className="bg-blue-50 p-4 rounded-md">
                            <div className="flex items-center">
                                <Mail className="h-5 w-5 text-blue-600 mr-2" />
                                <div>
                                    <p className="text-sm font-medium text-blue-900">
                                        Envoi d'un email de réinitialisation
                                    </p>
                                    <p className="text-sm text-blue-700 mt-1">
                                        Un email sera envoyé à <strong>{selectedUser?.email}</strong> avec un lien pour réinitialiser le mot de passe.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-yellow-50 p-4 rounded-md">
                            <p className="text-sm text-yellow-800">
                                <strong>Note:</strong> Le lien de réinitialisation expirera dans 60 minutes.
                            </p>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-6">
                        <Button
                            variant="outline"
                            onClick={() => setShowPasswordModal(false)}
                        >
                            Annuler
                        </Button>
                        <Button
                            onClick={handleResetPassword}
                        >
                            Envoyer l'email
                        </Button>
                    </div>
                </div>
            </div>
        )
    );

    const ConfirmToggleModal = () => {
        const cancelButtonRef = useRef(null);

        // Focus management for accessibility
        useEffect(() => {
            if (showConfirmModal && cancelButtonRef.current) {
                cancelButtonRef.current.focus();
            }
        }, [showConfirmModal]);

        return showConfirmModal && selectedUser && (
            <div
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                role="dialog"
                aria-modal="true"
                aria-labelledby="confirm-modal-title"
                aria-describedby="confirm-modal-description"
                onClick={(e) => {
                    if (e.target === e.currentTarget) {
                        setShowConfirmModal(false);
                        setSelectedUser(null);
                    }
                }}
            >
                <div className="bg-white rounded-lg p-4 sm:p-6 w-full max-w-md mx-4">
                    <div className="flex items-center mb-4">
                        {selectedUser.is_activated ? (
                            <UserX className="h-6 w-6 text-red-600 mr-3" />
                        ) : (
                            <UserCheck className="h-6 w-6 text-green-600 mr-3" />
                        )}
                        <h3
                            id="confirm-modal-title"
                            className="text-lg font-semibold text-gray-900"
                        >
                            {selectedUser.is_activated ? 'Désactiver' : 'Activer'} l'utilisateur
                        </h3>
                    </div>

                    <div className="space-y-4">
                        <p
                            id="confirm-modal-description"
                            className="text-sm text-gray-600"
                        >
                            Êtes-vous sûr de vouloir{' '}
                            <strong>
                                {selectedUser.is_activated ? 'désactiver' : 'activer'}
                            </strong>
                            {' '}l'utilisateur{' '}
                            <strong>{selectedUser.name}</strong> ?
                        </p>

                        <div className={`p-4 rounded-md ${
                            selectedUser.is_activated
                                ? 'bg-red-50 border border-red-200'
                                : 'bg-green-50 border border-green-200'
                        }`}>
                            <div className="flex items-start">
                                <div className={`flex-shrink-0 ${
                                    selectedUser.is_activated ? 'text-red-600' : 'text-green-600'
                                }`}>
                                    {selectedUser.is_activated ? (
                                        <UserX className="h-5 w-5" />
                                    ) : (
                                        <UserCheck className="h-5 w-5" />
                                    )}
                                </div>
                                <div className="ml-3">
                                    <p className={`text-sm font-medium ${
                                        selectedUser.is_activated ? 'text-red-900' : 'text-green-900'
                                    }`}>
                                        {selectedUser.is_activated
                                            ? 'Désactivation de l\'utilisateur'
                                            : 'Activation de l\'utilisateur'
                                        }
                                    </p>
                                    <p className={`text-sm mt-1 ${
                                        selectedUser.is_activated ? 'text-red-700' : 'text-green-700'
                                    }`}>
                                        {selectedUser.is_activated
                                            ? 'L\'utilisateur ne pourra plus se connecter à l\'application.'
                                            : 'L\'utilisateur pourra se connecter et utiliser l\'application.'
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-6">
                        <Button
                            ref={cancelButtonRef}
                            variant="outline"
                            onClick={() => {
                                setShowConfirmModal(false);
                                setSelectedUser(null);
                            }}
                            disabled={isToggling}
                            className="w-full sm:w-auto"
                        >
                            Annuler
                        </Button>
                        <Button
                            onClick={confirmToggleUserStatus}
                            disabled={isToggling}
                            className={`w-full sm:w-auto ${
                                selectedUser.is_activated
                                    ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                                    : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                            }`}
                        >
                            {isToggling ? (
                                <div className="flex items-center">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Traitement...
                                </div>
                            ) : (
                                <>
                                    {selectedUser.is_activated ? (
                                        <>
                                            <UserX className="h-4 w-4 mr-2" />
                                            Désactiver
                                        </>
                                    ) : (
                                        <>
                                            <UserCheck className="h-4 w-4 mr-2" />
                                            Activer
                                        </>
                                    )}
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Gestion des Utilisateurs" />

            <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Gestion des Utilisateurs</h1>
                        <p className="text-sm sm:text-base text-gray-600 mt-1">Créez, modifiez et gérez les utilisateurs et leurs rôles</p>
                    </div>
                    <Button onClick={() => setShowCreateModal(true)} className="w-full sm:w-auto">
                        <UserPlus className="h-4 w-4 mr-2" />
                        Nouvel Utilisateur
                    </Button>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 lg:gap-4">
                    <StatCard
                        title="Total"
                        value={userStats?.total_users || 0}
                        icon={Users}
                        color="text-blue-600"
                    />
                    <StatCard
                        title="Assistants"
                        value={userStats?.assistants || 0}
                        icon={UserCheck}
                        color="text-green-600"
                    />
                    <StatCard
                        title="Représentants"
                        value={userStats?.representatives || 0}
                        icon={UserCheck}
                        color="text-purple-600"
                    />
                    <StatCard
                        title="Recruteurs"
                        value={userStats?.recruiters || 0}
                        icon={UserCheck}
                        color="text-orange-600"
                    />
                    <StatCard
                        title="Exécutifs"
                        value={userStats?.executives || 0}
                        icon={UserCheck}
                        color="text-red-600"
                    />
                    <StatCard
                        title="Admins"
                        value={userStats?.admins || 0}
                        icon={Shield}
                        color="text-gray-600"
                    />
                </div>

                {/* Search and Filters */}
                <Card className="border-0 bg-white shadow-lg">
                    <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="Rechercher par nom ou email..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                    />
                                </div>
                            </div>
                            <div className="flex gap-2">
                                <Button 
                                    variant="outline" 
                                    onClick={() => setShowFilters(!showFilters)}
                                >
                                    <Filter className="h-4 w-4 mr-2" />
                                    Filtres
                                </Button>
                                <Button onClick={handleSearch}>
                                    Rechercher
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Effacer
                                </Button>
                            </div>
                        </div>

                        {showFilters && (
                            <div className="mt-4 pt-4 border-t">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Rôle
                                    </label>
                                    <select
                                        value={selectedRole}
                                        onChange={(e) => setSelectedRole(e.target.value)}
                                        className="w-full max-w-xs p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Tous les rôles</option>
                                        <option value="assistant">Assistant Commercial</option>
                                        <option value="representative">Représentant Commercial</option>
                                        <option value="recruiter">Recruteur</option>
                                        <option value="executive">Exécutif</option>
                                        <option value="admin">Administrateur</option>
                                    </select>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Users List */}
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    {users?.data?.map((user) => (
                        <UserCard key={user.id} user={user} />
                    ))}
                </div>

                {/* Pagination */}
                {users?.links && (
                    <div className="flex justify-center space-x-2">
                        {users.links.map((link, index) => (
                            <Button
                                key={index}
                                variant={link.active ? "default" : "outline"}
                                size="sm"
                                onClick={() => link.url && router.get(link.url)}
                                disabled={!link.url}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}

                {(!users?.data || users.data.length === 0) && (
                    <div className="text-center py-12">
                        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun utilisateur trouvé</h3>
                        <p className="text-gray-500">Essayez de modifier vos critères de recherche</p>
                    </div>
                )}
            </div>

            {/* Create User Modal */}
            {showCreateModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 className="text-lg font-semibold mb-4">Créer un nouvel utilisateur</h3>
                        <p className="text-sm text-gray-600 mb-4">
                            Un email avec les informations de connexion sera envoyé à l'utilisateur.
                        </p>

                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Nom complet
                                </label>
                                <input
                                    type="text"
                                    value={newUser.name}
                                    onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                                    className={`w-full p-2 border rounded-md focus:ring-2 focus:border-transparent ${
                                        errors?.name
                                            ? 'border-red-300 focus:ring-red-500'
                                            : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                                    placeholder="Ex: Jean Dupont"
                                />
                                {errors?.name && (
                                    <p className="mt-1 text-sm text-red-600">
                                        {errors.name}
                                    </p>
                                )}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Email
                                </label>
                                <input
                                    type="email"
                                    value={newUser.email}
                                    onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                                    className={`w-full p-2 border rounded-md focus:ring-2 focus:border-transparent ${
                                        errors?.email
                                            ? 'border-red-300 focus:ring-red-500'
                                            : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                                    placeholder="<EMAIL>"
                                />
                                {errors?.email && (
                                    <p className="mt-1 text-sm text-red-600">
                                        {errors.email}
                                    </p>
                                )}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Rôle
                                </label>
                                <select
                                    value={newUser.role}
                                    onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                                    className={`w-full p-2 border rounded-md focus:ring-2 focus:border-transparent ${
                                        errors?.role
                                            ? 'border-red-300 focus:ring-red-500'
                                            : 'border-gray-300 focus:ring-blue-500'
                                    }`}
                                >
                                    <option value="assistant">Assistant Commercial</option>
                                    <option value="representative">Représentant Commercial</option>
                                    <option value="recruiter">Recruteur</option>
                                    <option value="executive">Exécutif</option>
                                    <option value="admin">Administrateur</option>
                                </select>
                                {errors?.role && (
                                    <p className="mt-1 text-sm text-red-600">
                                        {errors.role}
                                    </p>
                                )}
                            </div>

                            <div className="bg-blue-50 p-3 rounded-md">
                                <p className="text-sm text-blue-800">
                                    <strong>Note:</strong> Un mot de passe temporaire sera généré et envoyé par email à l'utilisateur.
                                </p>
                            </div>
                        </div>

                        <div className="flex justify-end space-x-3 mt-6">
                            <Button
                                variant="outline"
                                onClick={() => setShowCreateModal(false)}
                            >
                                Annuler
                            </Button>
                            <Button
                                onClick={handleCreateUser}
                                disabled={!newUser.name || !newUser.email}
                            >
                                Créer l'utilisateur
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {/* Edit User Modal */}
            {showEditModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 className="text-lg font-semibold mb-4">Modifier le profil utilisateur</h3>
                        <p className="text-sm text-gray-600 mb-4">
                            Vous pouvez uniquement modifier le nom et l'email de l'utilisateur.
                        </p>

                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Nom complet
                                </label>
                                <input
                                    type="text"
                                    value={editUser.name}
                                    onChange={(e) => setEditUser({...editUser, name: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="Nom complet de l'utilisateur"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Email
                                </label>
                                <input
                                    type="email"
                                    value={editUser.email}
                                    onChange={(e) => setEditUser({...editUser, email: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="<EMAIL>"
                                />
                            </div>

                            <div className="bg-gray-50 p-3 rounded-md">
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Rôle actuel
                                </label>
                                <p className="text-sm text-gray-600">
                                    {getRoleBadge(selectedUser?.role)}
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                    Le rôle ne peut pas être modifié depuis cette interface.
                                </p>
                            </div>
                        </div>

                        <div className="flex justify-end space-x-3 mt-6">
                            <Button
                                variant="outline"
                                onClick={() => setShowEditModal(false)}
                            >
                                Annuler
                            </Button>
                            <Button
                                onClick={handleEditUser}
                                disabled={!editUser.name || !editUser.email}
                            >
                                Modifier le profil
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            <PasswordModal />
            <ConfirmToggleModal />
        </AppLayout>
    );
}
