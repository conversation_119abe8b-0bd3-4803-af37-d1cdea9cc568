import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { Award, Download, Euro } from 'lucide-react';

const breadcrumbs = [
    {
        title: 'Page de paie',
        href: '/payrollPage',
    },
];

export default function PayrollPage() {
    const { monthlyTotalBonus, thisSelectedMonth, topAssistant, assistantsData, bonusStats } = usePage().props;

    const getDataByMonth = (month) => {
        router.get('/payrollPage', { month });
    };
    const handleExport = () => {
        const exportMonth = thisSelectedMonth ? thisSelectedMonth.split('-')[1] : '';
        const exportYear = thisSelectedMonth ? thisSelectedMonth.split('-')[0] : '';
        console.log(thisSelectedMonth);
        if (!exportMonth || !exportYear) return;
        window.location.href = `/bonuses/export?month=${exportMonth}&year=${exportYear}`;
    };
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Payroll Page" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div>
                    <h1 className="text-3xl font-bold text-[#525e62]">Paie & Bonus</h1>
                    <p className="text-[#525e62]/70">Résumé mensuel des bonus par les assistants commerciaux & représentants commerciaux</p>
                </div>
                <div className="flex-1 p-6">
                    <div className="mx-auto max-w-7xl space-y-6">
                        {/* Month Selector */}
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <label htmlFor="month-select" className="text-sm font-medium">
                                    Sélectionner le mois :
                                </label>
                                <input
                                    value={thisSelectedMonth}
                                    id="month-select"
                                    onChange={(e) => getDataByMonth(e.target.value)}
                                    className="rounded-2xl border-[2px] p-[.5rem]"
                                    type="month"
                                />
                            </div>
                        </div>

                        <span className="flex flex-col gap-5">
                            {thisSelectedMonth && (
                                <>
                                    <div className="grid gap-6 md:grid-cols-3">
                                        {/* Total Bonuses */}
                                        <Card>
                                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                                <CardTitle className="text-sm font-medium">Total des bonus</CardTitle>
                                                <Euro className="text-muted-foreground h-4 w-4" />
                                            </CardHeader>
                                            <CardContent>
                                                <div className="text-2xl font-bold">€{monthlyTotalBonus}</div>
                                            </CardContent>
                                        </Card>

                                        {/* Most Rewarded Assistant */}
                                        <Card>
                                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                                <CardTitle className="text-sm font-medium">Assistant le plus récompensé</CardTitle>
                                                <Award className="text-muted-foreground h-4 w-4" />
                                            </CardHeader>
                                            <CardContent>
                                                <div className="text-2xl font-bold">{topAssistant && topAssistant.name}</div>
                                                <p className="text-muted-foreground text-xs">€{topAssistant && topAssistant.bonus} de bonus</p>
                                            </CardContent>
                                        </Card>

                                        {/* Export CSV */}
                                        <Card className="border-green-200 bg-green-50">
                                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                                <CardTitle className="text-sm font-medium text-green-800">Export des données</CardTitle>
                                                <Download className="h-4 w-4 text-green-600" />
                                            </CardHeader>
                                            <CardContent>
                                                <Button onClick={handleExport} className="w-full bg-green-600 hover:bg-green-700">
                                                    <Download className="mr-2 h-4 w-4" />
                                                    Exporter les bonuses de mois {thisSelectedMonth}
                                                </Button>
                                                <p className="mt-2 text-xs text-green-600">Télécharger les données</p>
                                            </CardContent>
                                        </Card>
                                    </div>

                                    {/* Bonus Table */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Détail des bonus par assistant</CardTitle>
                                            <CardDescription>Répartition des bonus pour {thisSelectedMonth}</CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="overflow-x-auto">
                                                <Table>
                                                    <TableHeader>
                                                        <TableRow>
                                                            <TableHead>Nom de l'assistant</TableHead>
                                                            <TableHead className="text-center">Bonus STANDARD</TableHead>
                                                            <TableHead className="text-center">Bonus LeBonCoin</TableHead>
                                                            <TableHead className="text-right">Total (€)</TableHead>
                                                        </TableRow>
                                                    </TableHeader>
                                                    <TableBody>
                                                        {assistantsData &&
                                                            assistantsData.map((assistant) => (
                                                                <TableRow key={assistant.id}>
                                                                    <TableCell className="font-medium">{assistant.name}</TableCell>
                                                                    <TableCell className="text-center">
                                                                        <div className="space-y-1">
                                                                            <Badge variant="secondary">{assistant.standardBonus} bonus</Badge>
                                                                            <div className="text-sm font-semibold">
                                                                                €
                                                                                {assistant.standardBonusAmount == null
                                                                                    ? '0'
                                                                                    : assistant.standardBonusAmount}
                                                                            </div>
                                                                        </div>
                                                                    </TableCell>
                                                                    <TableCell className="text-center">
                                                                        <div className="space-y-1">
                                                                            <Badge variant="outline">{assistant.lbcBonus} bonus</Badge>
                                                                            <div className="text-sm font-semibold">
                                                                                €{assistant.lbcBonusAmount == null ? '0' : assistant.lbcBonusAmount}
                                                                            </div>
                                                                        </div>
                                                                    </TableCell>
                                                                    <TableCell className="text-right font-bold">
                                                                        €{(assistant.standardBonusAmount + assistant.lbcBonusAmount).toLocaleString()}
                                                                    </TableCell>
                                                                </TableRow>
                                                            ))}
                                                    </TableBody>
                                                </Table>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Summary Statistics */}
                                    <div className="grid gap-6">
                                        <Card>
                                            <CardHeader>
                                                <CardTitle>Statistiques des bonus</CardTitle>
                                            </CardHeader>
                                            <CardContent className="space-y-4">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm">Nombre total de bonus</span>
                                                    <span className="font-semibold">{bonusStats && bonusStats.totalBonuses} Bonuses</span>
                                                </div>
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm">Total bonus STANDARD</span>
                                                    <span className="font-semibold">€{bonusStats && bonusStats.totalStandardBonus}</span>
                                                </div>
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm">Total bonus LeBonCoin</span>
                                                    <span className="font-semibold">€{bonusStats && bonusStats.totalLBCBonus}</span>
                                                </div>
                                                <div className="border-t pt-2">
                                                    <div className="flex items-center justify-between font-semibold">
                                                        <span>Montant total</span>
                                                        <span>€{monthlyTotalBonus && monthlyTotalBonus}</span>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </>
                            )}
                        </span>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
