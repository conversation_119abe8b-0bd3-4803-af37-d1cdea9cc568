import { useState } from 'react';
import { Head, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Users, 
    Calendar, 
    TrendingUp, 
    Award, 
    Target,
    BarChart3,
    PieChart,
    ArrowUpRight,
    ArrowDownRight,
    Crown
} from 'lucide-react';
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    BarChart,
    Bar,
    <PERSON><PERSON>hart as RechartsPieChart,
    Cell
} from 'recharts';

export default function Dashboard() {
    const { assistantsData, representativesData, pairingInsights, performanceTrends, weekPeriod } = usePage().props;
    const [activeTab, setActiveTab] = useState('overview');
    const COLORS = ['#525e62', '#8b9dc3', '#ddb892', '#f2cc8f'];

    const StatCard = ({ title, value, subtitle, icon: Icon, trend, color = "text-[#525e62]" }) => (
        <Card className="border-[#525e62]/10">
            <CardContent className="p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="text-sm font-medium text-[#525e62]/70">{title}</p>
                        <p className={`text-2xl font-bold ${color}`}>{value}</p>
                        {subtitle && <p className="text-xs text-[#525e62]/50">{subtitle}</p>}
                    </div>
                    <div className="h-12 w-12 rounded-lg bg-[#525e62]/10 flex items-center justify-center">
                        <Icon className="h-6 w-6 text-[#525e62]" />
                    </div>
                </div>
                {trend && (
                    <div className="mt-4 flex items-center">
                        {trend > 0 ? (
                            <ArrowUpRight className="h-4 w-4 text-green-600" />
                        ) : (
                            <ArrowDownRight className="h-4 w-4 text-red-600" />
                        )}
                        <span className={`text-sm ml-1 ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {Math.abs(trend)}% vs last week
                        </span>
                    </div>
                )}
            </CardContent>
        </Card>
    );

    const LeaderboardCard = ({ title, data, type }) => (
        <Card className="border-[#525e62]/10">
            <CardHeader>
                <CardTitle className="text-[#525e62] flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    {title}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-3">
                    {data.map((item, index) => (
                        <div key={item.id} className="flex items-center justify-between p-3 rounded-lg bg-[#f1efe0]/30">
                            <div className="flex items-center space-x-3">
                                <div className="relative">
                                    <div className="h-10 w-10 rounded-full bg-[#525e62] flex items-center justify-center text-white font-medium">
                                        {item.avatar}
                                    </div>
                                    {index === 0 && (
                                        <Crown className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1" />
                                    )}
                                </div>
                                <div>
                                    <p className="font-medium text-[#525e62]">{item.name}</p>
                                    <p className="text-sm text-[#525e62]/70">
                                        {type === 'assistant' 
                                            ? `${item.validated_appointments} validés`
                                            : `${item.total_purchases} achats`
                                        }
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="font-bold text-[#525e62]">
                                    {type === 'assistant' 
                                        ? `${item.validation_rate}%`
                                        : `${item.total_revenue?.toLocaleString()} €`
                                    }
                                </p>
                                <Badge variant="outline" className="text-xs">
                                    #{index + 1}
                                </Badge>
                            </div>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout>
            <Head title="Tableau de Bord Recruteur" />
            
            <div className="min-h-screen bgwhite p-6">
                <div className="max-w-7xl mx-auto space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-[#525e62]">Tableau de Bord Recruteur</h1>
                            <p className="text-[#525e62]/70">
                                Période: {weekPeriod.start} - {weekPeriod.end}
                            </p>
                        </div>
                        {/* <div className="flex space-x-2">
                            <Button 
                                variant={activeTab === 'overview' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('overview')}
                                className="bg-[#525e62] hover:bg-[#525e62]/90"
                            >
                                Vue d'ensemble
                            </Button>
                            <Button 
                                variant={activeTab === 'pairings' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('pairings')}
                                className="bg-[#525e62] hover:bg-[#525e62]/90"
                            >
                                Binômes
                            </Button>
                        </div> */}
                    </div>

                    {activeTab === 'overview' && (
                        <>
                            {/* Performance Overview Stats */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <StatCard
                                    title="Assistants"
                                    value={assistantsData.total_assistants}
                                    // subtitle=""
                                    icon={Users}
                                />
                                <StatCard
                                    title="RDV Cette semaine"
                                    value={assistantsData.total_appointments}
                                    subtitle={`${assistantsData.total_validated} validés`}
                                    icon={Calendar}
                                />
                                <StatCard
                                    title="Représentants"
                                    value={representativesData.total_representatives}
                                    // subtitle="Cette semaine"
                                    icon={Target}
                                />
                                <StatCard
                                    title="CA Généré"
                                    value={`${representativesData.total_revenue?.toLocaleString()} €`}
                                    subtitle={`${representativesData.total_purchases} achats`}
                                    icon={TrendingUp}
                                    color="text-green-600"
                                />
                            </div>

                            {/* Performance Trends Chart */}
                            <Card className="border-[#525e62]/10">
                                <CardHeader>
                                    <CardTitle className="text-[#525e62] flex items-center">
                                        <BarChart3 className="h-5 w-5 mr-2" />
                                        Évolution des Performances (4 dernières semaines)
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <LineChart data={performanceTrends}>
                                            <CartesianGrid strokeDasharray="3 3" stroke="#525e62/20" />
                                            <XAxis dataKey="week" stroke="#525e62" />
                                            <YAxis stroke="#525e62" />
                                            <Tooltip 
                                                contentStyle={{ 
                                                    backgroundColor: '#f1efe0', 
                                                    border: '1px solid #525e62',
                                                    borderRadius: '8px'
                                                }}
                                            />
                                            <Line 
                                                type="monotone" 
                                                dataKey="assistant_appointments" 
                                                stroke="#525e62" 
                                                strokeWidth={3}
                                                name="RDV Assistants"
                                            />
                                            <Line 
                                                type="monotone" 
                                                dataKey="rep_revenue" 
                                                stroke="#8b9dc3" 
                                                strokeWidth={3}
                                                name="CA Représentants"
                                            />
                                        </LineChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>

                            {/* Leaderboards */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <LeaderboardCard
                                    title="Top Assistants Commerciaux (Cette Semaine)"
                                    data={assistantsData.leaderboard}
                                    type="assistant"
                                />
                                <LeaderboardCard
                                    title="Top Représentants Commerciaux (Cette Semaine)"
                                    data={representativesData.leaderboard}
                                    type="representative"
                                />
                            </div>
                        </>
                    )}

                    {activeTab === 'pairings' && (
                        <>
                            {/* Pairing Insights Stats */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <StatCard
                                    title="Binômes Actifs"
                                    value={pairingInsights.total_pairings}
                                    subtitle="Cette semaine"
                                    icon={Users}
                                />
                                <StatCard
                                    title="Taux de Réussite Moyen"
                                    value={`${pairingInsights.avg_success_rate?.toFixed(1)}%`}
                                    subtitle="Tous binômes"
                                    icon={Target}
                                />
                                <StatCard
                                    title="CA Total Binômes"
                                    value={`${pairingInsights.total_revenue?.toLocaleString()} €`}
                                    subtitle="Cette semaine"
                                    icon={TrendingUp}
                                    color="text-green-600"
                                />
                            </div>

                            {/* Top Pair Highlight */}
                            {pairingInsights.top_pair && (
                                <Card className="border-[#525e62]/10 bg-gradient-to-r from-[#f1efe0] to-white">
                                    <CardHeader>
                                        <CardTitle className="text-[#525e62] flex items-center">
                                            <Crown className="h-5 w-5 mr-2 text-yellow-500" />
                                            Meilleur Binôme de la Semaine
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-4">
                                                <div className="flex items-center space-x-2">
                                                    <div className="h-12 w-12 rounded-full bg-[#525e62] flex items-center justify-center text-white font-medium">
                                                        {pairingInsights.top_pair.assistant_avatar}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-[#525e62]">
                                                            {pairingInsights.top_pair.assistant_name}
                                                        </p>
                                                        <p className="text-sm text-[#525e62]/70">Assistant</p>
                                                    </div>
                                                </div>
                                                <div className="text-2xl text-[#525e62]">+</div>
                                                <div className="flex items-center space-x-2">
                                                    <div className="h-12 w-12 rounded-full bg-[#8b9dc3] flex items-center justify-center text-white font-medium">
                                                        {pairingInsights.top_pair.representative_avatar}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-[#525e62]">
                                                            {pairingInsights.top_pair.representative_name}
                                                        </p>
                                                        <p className="text-sm text-[#525e62]/70">Représentant</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-2xl font-bold text-green-600">
                                                    {pairingInsights.top_pair.total_revenue?.toLocaleString()} €
                                                </p>
                                                <p className="text-sm text-[#525e62]/70">
                                                    {pairingInsights.top_pair.success_rate}% de réussite
                                                </p>
                                                <Badge className="mt-1">
                                                    {pairingInsights.top_pair.region}
                                                </Badge>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* All Pairings List */}
                            <Card className="border-[#525e62]/10">
                                <CardHeader>
                                    <CardTitle className="text-[#525e62]">Tous les Binômes</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {pairingInsights.all_pairings?.map((pairing, index) => (
                                            <div key={index} className="flex items-center justify-between p-4 rounded-lg bg-[#f1efe0]/30 border border-[#525e62]/10">
                                                <div className="flex items-center space-x-4">
                                                    <div className="flex items-center space-x-2">
                                                        <div className="h-10 w-10 rounded-full bg-[#525e62] flex items-center justify-center text-white text-sm font-medium">
                                                            {pairing.assistant_avatar}
                                                        </div>
                                                        <span className="font-medium text-[#525e62]">
                                                            {pairing.assistant_name}
                                                        </span>
                                                    </div>
                                                    <span className="text-[#525e62]">+</span>
                                                    <div className="flex items-center space-x-2">
                                                        <div className="h-10 w-10 rounded-full bg-[#8b9dc3] flex items-center justify-center text-white text-sm font-medium">
                                                            {pairing.representative_avatar}
                                                        </div>
                                                        <span className="font-medium text-[#525e62]">
                                                            {pairing.representative_name}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="flex items-center space-x-6">
                                                    <div className="text-center">
                                                        <p className="text-sm text-[#525e62]/70">Région</p>
                                                        <Badge variant="outline">{pairing.region}</Badge>
                                                    </div>
                                                    <div className="text-center">
                                                        <p className="text-sm text-[#525e62]/70">Taux de réussite</p>
                                                        <p className="font-bold text-[#525e62]">
                                                            {pairing.success_rate}%
                                                        </p>
                                                        <p className="text-xs text-[#525e62]/50">
                                                            {pairing.successful_appointments}/{pairing.total_appointments}
                                                        </p>
                                                    </div>
                                                    <div className="text-center">
                                                        <p className="text-sm text-[#525e62]/70">CA</p>
                                                        <p className="font-bold text-green-600">
                                                            {pairing.total_revenue?.toLocaleString()} €
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
