@echo off
echo.
echo ========================================
echo    AS-Shop Admin Test Data Seeder
echo ========================================
echo.
echo This will create fake data for testing admin pages:
echo - Users (Admin, Assistants, Representatives)
echo - Appointments (200 fake appointments)
echo - Purchases (with realistic conversion rates)
echo - Bonuses (linked to appointments and manual)
echo.
set /p choice="Do you want to run fresh migrations? (y/N): "
if /i "%choice%"=="y" (
    echo.
    echo Running with fresh migrations...
    php artisan admin:seed-test-data --fresh
) else (
    echo.
    echo Running without fresh migrations...
    php artisan admin:seed-test-data
)
echo.
echo ========================================
echo           Seeding Complete!
echo ========================================
echo.
echo You can now access the admin dashboard at:
echo http://localhost:8000/admin/dashboard
echo.
echo Login credentials:
echo Email: <EMAIL>
echo Password: password
echo.
pause
