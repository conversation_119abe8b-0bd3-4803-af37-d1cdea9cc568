import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { Award, Calendar, DollarSign, Download, Edit, Filter, Target, Trash2, TrendingUp, User, AlertTriangle } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';


const breadcrumbs = [
    {
        title: 'Administration',
        href: '/admin/dashboard',
    },
    {
        title: 'Système de bonus',
        href: '/admin/bonuses',
    },
];

export default function AdminBonuses() {
    const { bonuses, bonusSummary, assistants, filters } = usePage().props;

    const [dateFrom, setDateFrom] = useState(filters?.date_from || '');
    const [dateTo, setDateTo] = useState(filters?.date_to || '');
    const [selectedUser, setSelectedUser] = useState(filters?.user_id || '');
    const [selectedType, setSelectedType] = useState(filters?.type || '');
    const [showFilters, setShowFilters] = useState(false);
    const [exportMonth, setExportMonth] = useState('');
    const [exportYear, setExportYear] = useState('');
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [selectedBonus, setSelectedBonus] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);

    // Handle keyboard navigation for confirmation modal
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Escape' && showConfirmModal) {
                setShowConfirmModal(false);
                setSelectedBonus(null);
            }
        };

        if (showConfirmModal) {
            document.addEventListener('keydown', handleKeyDown);
            // Focus management for accessibility
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [showConfirmModal]);

    const handleFilter = () => {
        router.get(
            '/admin/bonuses',
            {
                date_from: dateFrom,
                date_to: dateTo,
                user_id: selectedUser,
                type: selectedType,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const handleExport = () => {
        if (!exportMonth || !exportYear) return;
        window.location.href = `/admin/bonuses/export?month=${exportMonth}&year=${exportYear}`;
    };

    const clearFilters = () => {
        setDateFrom('');
        setDateTo('');
        setSelectedUser('');
        setSelectedType('');
        router.get('/admin/bonuses');
    };

    const handleDeleteBonus = (bonus) => {
        setSelectedBonus(bonus);
        setShowConfirmModal(true);
    };

    const confirmDeleteBonus = () => {
        if (!selectedBonus) return;

        setIsDeleting(true);
        router.delete(`/admin/bonuses/${selectedBonus.id}`, {
            onSuccess: () => {
                setShowConfirmModal(false);
                setSelectedBonus(null);
                setIsDeleting(false);
                // Page will automatically refresh with the redirect and flash message
            },
            onError: (errors) => {
                console.error('Error deleting bonus:', errors);
                setIsDeleting(false);
                // Keep modal open on error so user can try again
            }
        });
    };

    const getTypeBadge = (type) => {
        const typeConfig = {
            STANDARD: { label: 'Standard', className: 'bg-blue-50 text-blue-700' },
            DAILY_SUPP: { label: 'Supplément', className: 'bg-purple-50 text-purple-700' },
        };

        const config = typeConfig[type] || { label: type, className: 'bg-gray-50 text-gray-700' };

        return (
            <Badge variant="outline" className={config.className}>
                {config.label}
            </Badge>
        );
    };

    const SummaryCard = ({ title, value, subtitle, icon: Icon, color = 'text-blue-600' }) => (
        <Card className="h-full border-0 bg-white shadow-sm transition-shadow hover:shadow-md">
            <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                    <div className="min-w-0 flex-1">
                        <p className="truncate text-xs font-medium text-gray-600 sm:text-sm">{title}</p>
                        <p className={`text-lg font-bold sm:text-2xl ${color} mt-1`}>{value}</p>
                        {subtitle && <p className="mt-1 truncate text-xs text-gray-500 sm:text-sm">{subtitle}</p>}
                    </div>
                    <div className="ml-3 flex-shrink-0 rounded-full bg-gray-50 p-2 sm:p-3">
                        <Icon className={`h-5 w-5 sm:h-6 sm:w-6 ${color}`} />
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const BonusCard = ({ bonus }) => (
        <Card className="border-0 bg-white shadow-sm transition-shadow hover:shadow-md">
            <CardContent className="p-4 sm:p-6">
                <div className="mb-4 flex flex-col items-start justify-between space-y-3 sm:flex-row sm:space-y-0">
                    <div className="flex min-w-0 flex-1 items-center space-x-3">
                        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-purple-100 sm:h-12 sm:w-12">
                            <Award className="h-5 w-5 text-purple-600 sm:h-6 sm:w-6" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="truncate text-sm font-semibold text-gray-900 sm:text-base">{bonus.user?.name}</h3>
                            <p className="truncate text-xs text-gray-500 sm:text-sm">{bonus.user?.email}</p>
                        </div>
                    </div>
                    <div className="flex flex-shrink-0 items-center space-x-2">
                        {getTypeBadge(bonus.type)}
                        <span className="text-base font-bold text-green-600 sm:text-lg">{bonus.amount} €</span>
                    </div>
                </div>

                <div className="mb-4 space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="mr-2 h-4 w-4" />
                        {new Date(bonus.created_at).toLocaleString('fr-FR')}
                    </div>
                    {bonus.appointment && (
                        <div className="flex items-center text-sm text-gray-600">
                            <Target className="mr-2 h-4 w-4" />
                            RDV: {bonus.appointment.clientName}
                        </div>
                    )}
                </div>

                {bonus.reason && (
                    <div className="mb-4">
                        <p className="rounded-lg bg-gray-50 p-3 text-sm text-gray-600">
                            <strong>Raison:</strong> {bonus.reason}
                        </p>
                    </div>
                )}

                <div className="flex justify-end space-x-2">
                    <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700" onClick={() => handleDeleteBonus(bonus)}>
                        <Trash2 className="mr-1 h-4 w-4" />
                        Supprimer
                    </Button>
                </div>
            </CardContent>
        </Card>
    );

    const ConfirmDeleteModal = () => {
        const cancelButtonRef = useRef(null);

        // Focus management for accessibility
        useEffect(() => {
            if (showConfirmModal && cancelButtonRef.current) {
                cancelButtonRef.current.focus();
            }
        }, [showConfirmModal]);

        return showConfirmModal && selectedBonus && (
            <div
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                role="dialog"
                aria-modal="true"
                aria-labelledby="confirm-modal-title"
                aria-describedby="confirm-modal-description"
                onClick={(e) => {
                    if (e.target === e.currentTarget) {
                        setShowConfirmModal(false);
                        setSelectedBonus(null);
                    }
                }}
            >
                <div className="bg-white rounded-lg p-4 sm:p-6 w-full max-w-md mx-4">
                    <div className="flex items-center mb-4">
                        <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
                        <h3
                            id="confirm-modal-title"
                            className="text-lg font-semibold text-gray-900"
                        >
                            Supprimer le bonus
                        </h3>
                    </div>

                    <div className="space-y-4">
                        <p
                            id="confirm-modal-description"
                            className="text-sm text-gray-600"
                        >
                            Êtes-vous sûr de vouloir supprimer ce bonus ?
                        </p>

                        <div className="p-4 rounded-md bg-red-50 border border-red-200">
                            <div className="flex items-start">
                                <div className="flex-shrink-0 text-red-600">
                                    <Trash2 className="h-5 w-5" />
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-red-900">
                                        Détails du bonus à supprimer
                                    </p>
                                    <div className="text-sm mt-1 text-red-700 space-y-1">
                                        <p><strong>Utilisateur:</strong> {selectedBonus.user?.name}</p>
                                        <p><strong>Type:</strong> {selectedBonus.type === 'STANDARD' ? 'Standard' : 'Supplément'}</p>
                                        <p><strong>Montant:</strong> {selectedBonus.amount}€</p>
                                        <p><strong>Raison:</strong> {selectedBonus.reason}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="p-3 rounded-md bg-yellow-50 border border-yellow-200">
                            <p className="text-sm text-yellow-800">
                                <strong>Attention:</strong> Cette action est irréversible. Le bonus sera définitivement supprimé.
                            </p>
                        </div>
                    </div>

                    <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-6">
                        <Button
                            ref={cancelButtonRef}
                            variant="outline"
                            onClick={() => {
                                setShowConfirmModal(false);
                                setSelectedBonus(null);
                            }}
                            disabled={isDeleting}
                            className="w-full sm:w-auto"
                        >
                            Annuler
                        </Button>
                        <Button
                            onClick={confirmDeleteBonus}
                            disabled={isDeleting}
                            className="w-full sm:w-auto bg-red-600 hover:bg-red-700 focus:ring-red-500"
                        >
                            {isDeleting ? (
                                <div className="flex items-center">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Suppression...
                                </div>
                            ) : (
                                <>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Supprimer
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Système de Bonus" />

            <div className="space-y-4 p-4 sm:space-y-6 sm:p-6">
                {/* Header */}
                <div className="flex flex-col items-start justify-between space-y-3 sm:flex-row sm:items-center sm:space-y-0">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl lg:text-3xl">Système de Bonus</h1>
                        <p className="mt-1 text-sm text-gray-600 sm:text-base">Gérez les bonus des assistants commerciaux</p>
                    </div>
                    <Dialog>
                        <DialogTrigger>
                            <Button className="w-full sm:w-auto">
                                <Download className="mr-2 h-4 w-4" />
                                Exporter
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Exporter les rendez-vous</DialogTitle>
                                <DialogDescription>Choisissez le mois et l'année à exporter.</DialogDescription>
                            </DialogHeader>
                            <div className="mt-4 flex flex-col gap-4">
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Mois</label>
                                    <select
                                        value={exportMonth}
                                        onChange={(e) => setExportMonth(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    >
                                        <option value="">Sélectionnez un mois</option>
                                        {[...Array(12)].map((_, i) => (
                                            <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                                                {new Date(0, i).toLocaleString('fr-FR', { month: 'long' })}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Année</label>
                                    <input
                                        type="number"
                                        min="2000"
                                        max={new Date().getFullYear()}
                                        value={exportYear}
                                        onChange={(e) => setExportYear(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                        placeholder="ex: 2025"
                                    />
                                </div>
                                <Button className="w-full" onClick={handleExport} disabled={!exportMonth || !exportYear}>
                                    <Download className="mr-2 h-4 w-4" />
                                    Exporter
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-4 lg:gap-6">
                    <SummaryCard
                        title="Total Aujourd'hui"
                        value={`${bonusSummary?.daily_total?.toLocaleString()} €`}
                        subtitle={`${bonusSummary?.daily_count} bonus`}
                        icon={DollarSign}
                        color="text-green-600"
                    />
                    <SummaryCard
                        title="Total Semaine"
                        value={`${bonusSummary?.weekly_total?.toLocaleString()} €`}
                        subtitle={`${bonusSummary?.weekly_count} bonus`}
                        icon={TrendingUp}
                        color="text-blue-600"
                    />
                    <SummaryCard
                        title="Total Mois"
                        value={`${bonusSummary?.monthly_total?.toLocaleString()} €`}
                        subtitle={`${bonusSummary?.monthly_count} bonus`}
                        icon={Award}
                        color="text-purple-600"
                    />
                    <SummaryCard
                        title="Moyenne/Assistant"
                        value={`${Math.round((bonusSummary?.monthly_total || 0) / Math.max(assistants?.length || 1, 1))} €`}
                        subtitle="Ce mois"
                        icon={User}
                        color="text-orange-600"
                    />
                </div>

                {/* Breakdown by Type and Source */}
                <div className="grid grid-cols-1 gap-3 sm:gap-4 lg:grid-cols-2 lg:gap-6">
                    <Card className="border-0 bg-white shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-[#525e62]">Répartition par Type (tous les temps)</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {bonusSummary?.by_type?.map((typeData, index) => (
                                    <div key={index} className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                                        <div className="flex items-center space-x-3">
                                            {getTypeBadge(typeData.type)}
                                            <span className="font-medium">{typeData.count} bonus</span>
                                        </div>
                                        <span className="font-bold text-green-600">{typeData.total?.toLocaleString()} €</span>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-0 bg-white shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-[#525e62]">Répartition par Source</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {bonusSummary?.by_source?.map((sourceData, index) => (
                                    <div key={index} className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                                        <div className="flex items-center space-x-3">
                                            <Badge
                                                variant="outline"
                                                className={
                                                    sourceData.source === 'leboncoin' ? 'bg-blue-50 text-blue-700' : 'bg-green-50 text-green-700'
                                                }
                                            >
                                                {sourceData.source === 'leboncoin' ? 'LBC' : 'APPEL'}
                                            </Badge>
                                            <span className="font-medium">{sourceData.count} bonus</span>
                                        </div>
                                        <span className="font-bold text-green-600">{sourceData.total?.toLocaleString()} €</span>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card className="border-0 bg-white p-0 shadow-lg">
                    <CardContent className="p-4 sm:p-6">
                        <div className={`flex items-center justify-between ${showFilters ? 'mb-4' : ''} `}>
                            <h3 className="font-semibold text-gray-900">Filtres</h3>
                            <div className="flex flex-col justify-end space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                                <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)}>
                                    <Filter className="mr-2 h-4 w-4" />
                                    {showFilters ? 'Masquer' : 'Afficher'} filtres
                                </Button>
                                <Button variant="outline" onClick={clearFilters} className="w-full sm:w-auto">
                                    Effacer
                                </Button>
                                <Button onClick={handleFilter} className="w-full sm:w-auto">
                                    Appliquer
                                </Button>
                            </div>
                        </div>

                        {showFilters && (
                            <div className="grid grid-cols-1 gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-4">
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Date début</label>
                                    <input
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    />
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Date fin</label>
                                    <input
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    />
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Assistant</label>
                                    <select
                                        value={selectedUser}
                                        onChange={(e) => setSelectedUser(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    >
                                        <option value="">Tous les assistants</option>
                                        {assistants?.map((assistant) => (
                                            <option key={assistant.id} value={assistant.id}>
                                                {assistant.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">Type</label>
                                    <select
                                        value={selectedType}
                                        onChange={(e) => setSelectedType(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 p-2"
                                    >
                                        <option value="">Tous les types</option>
                                        <option value="STANDARD">Standard</option>
                                        <option value="DAILY_SUPP">Supplément</option>
                                    </select>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Bonuses List */}
                <div className="grid grid-cols-1 gap-3 sm:gap-4 lg:grid-cols-2 lg:gap-6">
                    {bonuses?.data?.map((bonus) => (
                        <BonusCard key={bonus.id} bonus={bonus} />
                    ))}
                </div>

                {/* Pagination */}
                {bonuses?.links && (
                    <div className="flex flex-wrap justify-center gap-2">
                        {bonuses.links.map((link, index) => (
                            <Button
                                key={index}
                                variant={link.active ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => link.url && router.get(link.url)}
                                disabled={!link.url}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}

                {(!bonuses?.data || bonuses.data.length === 0) && (
                    <div className="py-12 text-center">
                        <Award className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                        <h3 className="mb-2 text-lg font-medium text-gray-900">Aucun bonus trouvé</h3>
                        <p className="text-gray-500">Essayez de modifier vos critères de recherche</p>
                    </div>
                )}
            </div>
            <ConfirmDeleteModal />
        </AppLayout>
    );
}
