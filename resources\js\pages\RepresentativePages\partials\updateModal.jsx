import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from '@inertiajs/react';
import { useEffect } from 'react';

const UpdateModal = ({ open, selectedEntry, setEditModal }) => {
    // Extract data from the nested structure
    const getInitialData = () => {
        if (!selectedEntry)
            return {
                description: '',
                type: '',
                weight: '',
                purchasePrice: '',
                estimatedValue: '',
                status: 'not_purchased',
                notes: '',
            };

        // The data comes in a nested structure: selectedEntry.items[0] contains the actual item data
        const item = selectedEntry.items?.[0] || {};

        return {
            description: item.description || '',
            type: selectedEntry.type || '',
            weight: item.weight || '',
            buy_price: item.purchasePrice || '',
            resale_price: item.estimatedValue || '',
            status: selectedEntry.status || 'not_purchased',
            notes: '',
        };
    };

    const { data, setData, put, processing, errors } = useForm(getInitialData());

    // Sync form when entry changes
    useEffect(() => {
        if (selectedEntry) {
            const item = selectedEntry.items?.[0] || {};
            setData({
                description: item.description || '',
                type: selectedEntry.type || '',
                weight: item.weight || '',
                buy_price: item.purchasePrice || '',
                resale_price: item.estimatedValue || '',
                status: selectedEntry.status || 'not_purchased',
                notes: '', // Notes are not currently in the data structure
            });
        }
    }, [selectedEntry, setData]);

    const handleUpdate = (e) => {
        e.preventDefault();
        console.log('=== UPDATE DEBUG INFO ===');
        console.log('Selected entry:', selectedEntry);
        console.log('Selected entry ID:', selectedEntry?.id);
        console.log('Data being sent:', data);
        console.log('Route URL:', route('representative.purchase.update', selectedEntry?.id));
        console.log('========================');

        if (!selectedEntry?.id) {
            alert('Error: No purchase ID found!');
            return;
        }

        // Replace with your update route and id
        put(route('representative.purchase.update', selectedEntry.id), {
            onSuccess: (response) => {
                console.log('Update successful', response);
                setEditModal(false);
                // Force page refresh to see changes
            },
            onError: (errors) => {
                console.log('Update errors:', errors);
                alert('Update failed: ' + JSON.stringify(errors));
            },
            onFinish: () => {
                console.log('Update request finished');
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={() => setEditModal(null)}>
            <DialogContent className="max-h-[90vh] overflow-y-auto">
            {/* <DialogHeader>
                <DialogTitle className="flex items-center justify-between text-[#525e62]">Modifier - {selectedEntry?.clientName}</DialogTitle>
            </DialogHeader> */}
                <form onSubmit={handleUpdate} className="space-y-4 ">
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <Label className="text-[#525e62]">Date et heure</Label>
                            <div className="font-medium text-[#525e62]">
                                {selectedEntry?.date} à {selectedEntry?.time}
                            </div>
                        </div>
                        <div>
                            <Label className="text-[#525e62]">Type (Non modifiable)</Label>
                            <div className="rounded border bg-gray-100 p-2 font-medium text-[#525e62]">{selectedEntry?.type}</div>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label className="text-[#525e62]">Statut</Label>
                        <Select value={data.status} onValueChange={(val) => setData('status', val)}>
                            <SelectTrigger>
                                <SelectValue placeholder="Sélectionnez le statut" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="purchased">Acheté</SelectItem>
                                <SelectItem value="not_purchased">Non acheté</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label className="text-[#525e62]">Description</Label>
                        <Input
                            value={data.description}
                            onChange={(e) => setData('description', e.target.value)}
                            placeholder="Description détaillée"
                        />
                    </div>
                    <div className="space-y-2">
                        <Label className="text-[#525e62]">Poids (g)</Label>
                        <Input type="number" step="0.1" value={data.weight} onChange={(e) => setData('weight', e.target.value)} placeholder="0.0" />
                    </div>
                    <div className="space-y-2">
                        <Label className="text-[#525e62]">Prix de rachat (€)</Label>
                        <Input type="number" value={data.buy_price} onChange={(e) => setData('buy_price', e.target.value)} placeholder="0" />
                    </div>
                    <div className="space-y-2">
                        <Label className="text-[#525e62]">Valeur estimée (€)</Label>
                        <Input type="number" value={data.resale_price} onChange={(e) => setData('resale_price', e.target.value)} placeholder="0" />
                    </div>
                    <div className="space-y-2">
                        <Label className="text-[#525e62]">Notes et observations</Label>
                        <Textarea
                            value={data.notes}
                            onChange={(e) => setData('notes', e.target.value)}
                            rows={4}
                            placeholder="Notes sur le rendez-vous, comportement client, remarques particulières..."
                        />
                    </div>
                    <div className="flex justify-end space-x-3 border-t border-[#525e62]/10 pt-6">
                        <Button variant="outline" type="button" onClick={() => setEditModal(false)}>
                            Annuler
                        </Button>
                        <Button type="submit" className="bg-[#525e62] text-white hover:bg-[#525e62]/90" disabled={processing}>
                            Enregistrer
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
};

export default UpdateModal;
