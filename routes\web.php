<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified', 'activated'])->group(function () {
    Route::get('dashboard', function () {
        $user = Auth::user();

        // Define role-based redirects to first route in each role's sidebar
        $roleRedirects = [
            'assistant' => '/welcomeAssistant',
            'representative' => '/representative/dashboard',
            'recruiter' => '/recruiter/dashboard',
            'executive' => '/welcomeExecutive',
            'admin' => '/admin/dashboard',
        ];

        // Redirect to role-specific dashboard if user has a defined role
        if (isset($roleRedirects[$user->role])) {
            return redirect($roleRedirects[$user->role]);
        }

        // Fallback to generic dashboard for unknown roles
        return Inertia::render('dashboard');
    })->name('dashboard');
});

// ? Assistant Routes

Route::inertia('/welcomeAssistant','AssistantPages/welcomeAssistant');
Route::inertia('/appointmentCreation','AssistantPages/appointmentCreation');
Route::inertia('/appointmentsHistory','AssistantPages/appointmentsHistory');
Route::inertia('/assistantPerformance','AssistantPages/assistantPerformance');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/assistant.php';
require __DIR__.'/representative.php';
require __DIR__.'/recruiter.php';
require __DIR__.'/executive.php';
require __DIR__.'/admin.php';
