import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { Link, usePage } from '@inertiajs/react';

export function NavMain({ items = [] }) {
    const page = usePage();

    // ? The next 3 line are for knowing the links for the auth user (<PERSON><PERSON> li codaha machi)

    const { auth } = usePage().props;

    let authRole = auth.user?.role

    
    
    let authenticatedRoleLinks = items?.filter(item => item.permission == authRole)
    

    return (
        <SidebarGroup className="px-2 py-0">
            <SidebarGroupLabel>Platform</SidebarGroupLabel>
            <SidebarMenu>
                {authenticatedRoleLinks.map((roleLink) => (
                    <SidebarMenuItem key={roleLink.title}>
                        <SidebarMenuButton asChild isActive={page.url.startsWith(roleLink.url)}>
                            <Link className={``} href={roleLink.url} prefetch>
                                {roleLink.icon && <roleLink.icon />}
                                <span>{roleLink.title}</span>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
}
