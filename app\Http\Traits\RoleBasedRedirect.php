<?php

namespace App\Http\Traits;

trait RoleBasedRedirect
{
    /**
     * Get the role-based redirect URL for a user
     * 
     * @param \App\Models\User $user
     * @return string
     */
    protected function getRoleBasedRedirectUrl($user): string
    {
        // Define role-based redirects mapping to first route in each role's sidebar
        $roleRedirects = [
            'assistant' => '/welcomeAssistant',           // Tableau de bord
            'representative' => '/representative/dashboard', // Dashboard
            'recruiter' => '/recruiter/dashboard',        // Dashboard
            'executive' => '/welcomeExecutive',           // Dashboard
            'admin' => '/admin/dashboard',                // Tableau de bord global
        ];

        // Return the redirect URL for the user's role, fallback to generic dashboard
        return $roleRedirects[$user->role] ?? route('dashboard', absolute: false);
    }

    /**
     * Redirect to the user's role-based dashboard
     * 
     * @param \App\Models\User $user
     * @param string $queryString Optional query string to append
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function redirectToRoleDashboard($user, string $queryString = ''): \Illuminate\Http\RedirectResponse
    {
        $redirectUrl = $this->getRoleBasedRedirectUrl($user);
        
        if ($queryString) {
            $redirectUrl .= $queryString;
        }

        return redirect()->intended($redirectUrl);
    }
}
