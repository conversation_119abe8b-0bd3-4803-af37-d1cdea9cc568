<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssistantRepresentativeAssignment extends Model
{
    protected $fillable = [
        'assistant_id',
        'representative_id',
    ];

    public function assistant(): <PERSON>ongsT<PERSON>
    {
        return $this->belongsTo(User::class, 'assistant_id');
    }

    public function representative(): BelongsTo
    {
        return $this->belongsTo(User::class, 'representative_id');
    }
}
