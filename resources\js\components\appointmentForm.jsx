import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus } from "lucide-react"
import { useForm, usePage } from "@inertiajs/react"
import { useState } from "react"

export function AppointmentForm() {

    const { auth, salesReps } = usePage().props

    const [selectedItems, setSelectedItems] = useState([]);

    const { data, setData, post, processing, errors, reset } = useForm({
        assistant_id: auth.user?.id,
        representative_id: 0,
        clientName: "",
        clientPhone: "",
        clientAddress: "",
        source: "",
        dateTime: "",
        itemsCollection: selectedItems,
        notes: "",
        appointment_type: ""
    });

    const items = [
        "lustres anciens, chandelier",
        "pendule, horloge comtoise",
        "montre et montre a gousset (en argent et en or)",
        "gueridon, sellette, coiffeuse",
        "vaisselle ancienne (porcelaine, cuivre, etain, cristal)",
        "tableaux anciens",
        "livres anciens (missels etc)",
        "manteaux de fourrure veritable",
        "toque, etole, echarpe en fourrure",
        "bijoux de fantaisie",
        "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
        "accessoires anciens (boutons de manchette, pince a cravate, broche, epingle a chapeau, came)",
        "art asiatique (vase chinois)",
        "pieces de monnaie (etrangeres, en or ou franc)",
        "machine a coudre"
    ];

    const toggleItem = (item) => {
        let newItemsCollection

        if (selectedItems.includes(item)) {
            // remove it
            newItemsCollection = selectedItems.filter((i) => i !== item);
        } else {
            // add it
            newItemsCollection = [...selectedItems, item];
        }
        setSelectedItems(newItemsCollection)
        setData({ ...data, itemsCollection: newItemsCollection })
    };

    let now = new Date().toISOString().slice(0, 16);


    const handleSubmit = (e) => {
        e.preventDefault()
        setData({ ...data, itemsCollection: selectedItems })
        post(route("appointments.store"), {
            preserveState: false,
            onSuccess: () => {
                reset();
                setData('assistant_id', auth.user?.id);
            }
        })
    }

    return (
        <Card className="bg-white border-0 shadow-lg">
            <CardHeader>
                <CardTitle className="text-[#525e62] flex items-center">
                    <Plus className="mr-2 h-5 w-5" />
                    Nouveau Rendez-vous
                </CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} encType="multipart/form-data" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="clientName" className="text-[#525e62]">
                                Nom du client
                            </Label>
                            <Input
                                id="clientName"
                                value={data.clientName}
                                onChange={(e) => setData({ ...data, clientName: e.target.value })}
                                className="border-[#525e62]/20 focus:border-[#525e62]"
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="clientPhone" className="text-[#525e62]">
                                Téléphone du client
                            </Label>
                            <Input
                                id="clientPhone"
                                type="tel"
                                value={data.clientPhone}
                                onChange={(e) => setData({ ...data, clientPhone: e.target.value })}
                                className="border-[#525e62]/20 focus:border-[#525e62]"
                                required
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="clientAddress" className="text-[#525e62]">
                            Adresse du client
                        </Label>
                        <Input
                            id="clientAddress"
                            value={data.clientAddress}
                            onChange={(e) => setData({ ...data, clientAddress: e.target.value })}
                            className="border-[#525e62]/20 focus:border-[#525e62]"
                            placeholder="Adresse complète du client"
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="source" className="text-[#525e62]">
                            Source
                        </Label>
                        <Select value={data.source} onValueChange={(value) => setData({ ...data, source: value })}>
                            <SelectTrigger className="border-[#525e62]/20 focus:border-[#525e62]">
                                <SelectValue placeholder="Sélectionnez la source" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="outbound">Sortant</SelectItem>
                                <SelectItem value="leboncoin">LeBonCoin</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="datetime" className="text-[#525e62]">
                            Date & Heure
                        </Label>
                        <Input
                            id="datetime"
                            type="datetime-local"
                            value={data.dateTime}
                            onChange={(e) => setData({ ...data, dateTime: e.target.value })}
                            className="border-[#525e62]/20 focus:border-[#525e62]"
                            required
                            min={now}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="appointmentType" className="text-[#525e62]">
                            Type de rendez-vous
                        </Label>
                        <Select value={data.appointment_type} onValueChange={(value) => setData({ ...data, appointment_type: value })}>
                            <SelectTrigger className="border-[#525e62]/20 focus:border-[#525e62]">
                                <SelectValue placeholder="Sélectionnez le type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="announced">Annoncé</SelectItem>
                                <SelectItem value="not_announced">Non annoncé</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="representative_id" className="text-[#525e62]">
                            Assigné à
                        </Label>
                        {salesReps && salesReps.length > 0 && (
                            <p className="text-xs text-gray-500">
                                Représentants assignés à votre compte ({salesReps.length})
                            </p>
                        )}
                        <Select
                            value={data.representative_id}
                            onValueChange={(value) => setData({ ...data, representative_id: Number(value) })}
                        >
                            <SelectTrigger className="border-[#525e62]/20 focus:border-[#525e62]">
                                <SelectValue placeholder="Sélectionnez un commercial" />
                            </SelectTrigger>
                            <SelectContent>
                                {salesReps && salesReps.length > 0 ? (
                                    salesReps.map((rep) => (
                                        <SelectItem key={rep.id} value={Number(rep.id)}>
                                            {rep.name}
                                        </SelectItem>
                                    ))
                                ) : (
                                    <SelectItem value={0} disabled>
                                        Aucun représentant assigné
                                    </SelectItem>
                                )}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="items" className="text-[#525e62]">
                            Objets
                        </Label>
                        <div className="flex flex-wrap gap-2 mb-4">
                            {items.map((item) => {
                                const isSelected = selectedItems.includes(item);
                                return (
                                    <button
                                        type="button"
                                        key={item}
                                        onClick={() => toggleItem(item)}
                                        className={`text-sm px-3 py-1 rounded transition ${isSelected ? 'bg-green-200 text-green-900' : 'bg-gray-100 hover:bg-gray-200'}`}
                                    >
                                        {item}
                                    </button>
                                );
                            })}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="notes" className="text-[#525e62]">
                            Notes
                        </Label>
                        <Textarea
                            id="notes"
                            value={data.notes}
                            onChange={(e) => setData({ ...data, notes: e.target.value })}
                            className="border-[#525e62]/20 focus:border-[#525e62]"
                            rows={3}
                            placeholder="Notes supplémentaires sur le rendez-vous"
                        />
                    </div>

                    <Button disabled={processing} type="submit" className="w-full bg-[#525e62] hover:bg-[#525e62]/90 text-white">
                        {
                            processing ? "Création du rendez-vous" : "Créer le rendez-vous"
                        }
                    </Button>
                </form>
            </CardContent>
        </Card>
    )
}
