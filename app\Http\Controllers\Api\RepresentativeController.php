<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\RepresentativeRankingResource;
use App\Http\Traits\ApiResponse;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepresentativeController extends Controller
{
    use ApiResponse;

    /**
     * Get weekly team rankings for representatives
     *
     * Returns top 10 representatives ranked by weekly performance
     * based on completed appointments and revenue for current week (Monday to Sunday)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function weeklyRankings(Request $request): JsonResponse
    {
        try {
            // Get current week start (Monday) and end (Sunday)
            $now = Carbon::now();
            $weekStart = $now->copy()->startOfWeek(Carbon::MONDAY);
            $weekEnd = $now->copy()->endOfWeek(Carbon::SUNDAY);

            // Get top 10 representatives with their weekly performance
            $representatives = User::select([
                'users.id',
                'users.name',
                'users.email',
                'users.created_at',
                DB::raw('COUNT(DISTINCT appointments.id) as total_appointments'),
                DB::raw('COUNT(DISTINCT CASE WHEN purchases.status = "purchased" THEN appointments.id END) as completed_appointments'),
                DB::raw('COALESCE(SUM(CASE WHEN purchases.status = "purchased" THEN purchases.resale_price END), 0) as weekly_revenue'),
                DB::raw('CASE
                    WHEN COUNT(DISTINCT appointments.id) > 0
                    THEN ROUND((COUNT(DISTINCT CASE WHEN purchases.status = "purchased" THEN appointments.id END) / COUNT(DISTINCT appointments.id)) * 100, 1)
                    ELSE 0
                END as completion_rate')
            ])
            ->where('users.role', 'representative')
            ->where('users.is_activated', true)
            ->leftJoin('appointments', function($join) use ($weekStart, $weekEnd) {
                $join->on('users.id', '=', 'appointments.representative_id')
                     ->whereBetween('appointments.dateTime', [$weekStart, $weekEnd]);
            })
            ->leftJoin('purchases', function($join) {
                $join->on('appointments.id', '=', 'purchases.appointment_id');
            })
            ->groupBy('users.id', 'users.name', 'users.email', 'users.created_at')
            ->orderByDesc('completed_appointments')
            ->orderByDesc('weekly_revenue')
            ->limit(10)
            ->get();

            // Add ranking position to each representative
            $rankedRepresentatives = $representatives->map(function ($representative, $index) {
                $representative->rank = $index + 1;
                return $representative;
            });

            // Prepare response with metadata
            $responseData = [
                'rankings' => RepresentativeRankingResource::collection($rankedRepresentatives),
                'meta' => [
                    'week_period' => [
                        'start' => $weekStart->toDateString(),
                        'end' => $weekEnd->toDateString(),
                        'week_number' => $weekStart->weekOfYear,
                        'year' => $weekStart->year,
                    ],
                    'total_representatives' => $representatives->count(),
                    'generated_at' => now()->toISOString(),
                ]
            ];

            return $this->successResponse(
                $responseData,
                'Weekly team rankings retrieved successfully'
            );

        } catch (\Exception $e) {
            Log::error('Error retrieving weekly rankings: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve weekly rankings');
        }
    }
}
