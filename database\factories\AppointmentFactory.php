<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Appointment>
 */
class AppointmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clientName' => $this->faker->name(),
            'clientPhone' => $this->faker->phoneNumber(),
            'clientAddress' => $this->faker->address(),
            'source' => $this->faker->randomElement(['outbound', 'leboncoin']),
            'dateTime' => $this->faker->dateTimeBetween('now', '+1 month'),
            'notes' => $this->faker->optional()->sentence(),
            'itemsCollection' => ['bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)'],
            'appointment_type' => $this->faker->randomElement(['announced', 'not_announced']),
        ];
    }
}
