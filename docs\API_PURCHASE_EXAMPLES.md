# Purchase API - Store Purchase Examples

## Overview
The Purchase API allows you to record purchase transactions for appointments. This endpoint is typically used by representatives after meeting with clients.

## Endpoint Details

**URL:** `POST /api/v1/purchases`  
**Authentication:** Required (<PERSON><PERSON>)  
**Role:** Any authenticated user (typically representatives)

## Request Examples

### Example 1: Successful Purchase (Item Bought)

```javascript
// JavaScript/React Native Example
const storePurchase = async (purchaseData, token) => {
    try {
        const response = await fetch('/api/v1/purchases', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                appointment_id: 123,
                status: "purchased",
                description: "Collier en or 18 carats avec pendentif diamant",
                item_type: "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
                weight: 25.5,
                buy_price: 450.00,
                resale_price: 750.00,
                benefit: 300.00,
                notes: "Excellent état, pièce authentique vérifiée"
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('Purchase recorded:', result.data);
            return result.data;
        } else {
            console.error('Error:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Network error:', error);
        throw error;
    }
};

// Usage
const purchaseData = {
    appointment_id: 123,
    status: "purchased",
    description: "Collier en or 18 carats avec pendentif diamant",
    item_type: "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
    weight: 25.5,
    buy_price: 450.00,
    resale_price: 750.00,
    benefit: 300.00,
    notes: "Excellent état, pièce authentique vérifiée"
};

storePurchase(purchaseData, userToken);
```

### Example 2: No Purchase Made (Client Declined)

```javascript
const storeNoPurchase = async (token) => {
    const response = await fetch('/api/v1/purchases', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            appointment_id: 124,
            status: "not_purchased",
            description: "Bague en argent avec pierre bleue",
            item_type: "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
            weight: 8.2,
            buy_price: null,
            resale_price: 120.00, // Estimated value
            benefit: null,
            notes: "Client a décliné l'offre, souhaite réfléchir"
        })
    });
    
    return await response.json();
};
```

### Example 3: Multiple Items Purchase

```javascript
const storeMultiplePurchases = async (appointmentId, items, token) => {
    const purchases = [];
    
    for (const item of items) {
        const response = await fetch('/api/v1/purchases', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                appointment_id: appointmentId,
                status: item.purchased ? "purchased" : "not_purchased",
                description: item.description,
                item_type: item.type,
                weight: item.weight,
                buy_price: item.purchased ? item.buyPrice : null,
                resale_price: item.estimatedValue,
                benefit: item.purchased ? (item.estimatedValue - item.buyPrice) : null,
                notes: item.notes
            })
        });
        
        const result = await response.json();
        if (result.success) {
            purchases.push(result.data);
        }
    }
    
    return purchases;
};

// Usage with multiple items
const items = [
    {
        description: "Montre Rolex Submariner",
        type: "montres",
        weight: 150.0,
        purchased: true,
        buyPrice: 2500.00,
        estimatedValue: 4200.00,
        notes: "Authentique, bon état général"
    },
    {
        description: "Bracelet en or blanc",
        type: "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
        weight: 12.3,
        purchased: false,
        buyPrice: null,
        estimatedValue: 180.00,
        notes: "Client souhaite garder pour l'instant"
    }
];

storeMultiplePurchases(125, items, userToken);
```

## React Native Complete Example

```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';

class PurchaseService {
    constructor() {
        this.baseURL = 'https://your-api-domain.com/api/v1';
    }

    async getAuthToken() {
        return await AsyncStorage.getItem('auth_token');
    }

    async createPurchase(purchaseData) {
        try {
            const token = await this.getAuthToken();
            
            const response = await fetch(`${this.baseURL}/purchases`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(purchaseData)
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || 'Failed to create purchase');
            }

            return result;
        } catch (error) {
            console.error('Purchase creation error:', error);
            throw error;
        }
    }

    // Helper method to calculate benefit
    calculateBenefit(buyPrice, resalePrice) {
        if (!buyPrice || !resalePrice) return null;
        return resalePrice - buyPrice;
    }

    // Helper method to format purchase data
    formatPurchaseData(appointmentId, item, purchased = true) {
        return {
            appointment_id: appointmentId,
            status: purchased ? "purchased" : "not_purchased",
            description: item.description,
            item_type: item.type,
            weight: item.weight,
            buy_price: purchased ? item.buyPrice : null,
            resale_price: item.estimatedValue,
            benefit: purchased ? this.calculateBenefit(item.buyPrice, item.estimatedValue) : null,
            notes: item.notes || ''
        };
    }
}

// Usage in React Native component
const PurchaseScreen = () => {
    const purchaseService = new PurchaseService();

    const handlePurchaseSubmit = async (formData) => {
        try {
            const purchaseData = purchaseService.formatPurchaseData(
                formData.appointmentId,
                {
                    description: formData.description,
                    type: formData.itemType,
                    weight: parseFloat(formData.weight),
                    buyPrice: parseFloat(formData.buyPrice),
                    estimatedValue: parseFloat(formData.estimatedValue),
                    notes: formData.notes
                },
                formData.purchased
            );

            const result = await purchaseService.createPurchase(purchaseData);
            
            if (result.success) {
                Alert.alert('Success', 'Purchase recorded successfully');
                // Navigate back or update UI
            }
        } catch (error) {
            Alert.alert('Error', error.message);
        }
    };

    return (
        // Your React Native form component here
    );
};
```

## cURL Examples

### Successful Purchase
```bash
curl -X POST "https://your-api-domain.com/api/v1/purchases" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "appointment_id": 123,
    "status": "purchased",
    "description": "Collier en or 18 carats",
    "item_type": "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
    "weight": 25.5,
    "buy_price": 450.00,
    "resale_price": 750.00,
    "benefit": 300.00,
    "notes": "Excellent état"
  }'
```

### No Purchase Made
```bash
curl -X POST "https://your-api-domain.com/api/v1/purchases" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "appointment_id": 124,
    "status": "not_purchased",
    "description": "Bague en argent",
    "item_type": "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
    "weight": 8.2,
    "buy_price": null,
    "resale_price": 120.00,
    "benefit": null,
    "notes": "Client a décliné"
  }'
```

## Response Examples

### Success Response (201 Created)
```json
{
    "success": true,
    "message": "Purchase created successfully",
    "data": {
        "id": 456,
        "appointment_id": 123,
        "status": "purchased",
        "description": "Collier en or 18 carats avec pendentif diamant",
        "item_type": "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
        "weight": 25.5,
        "buy_price": 450.00,
        "resale_price": 750.00,
        "benefit": 300.00,
        "notes": "Excellent état, pièce authentique vérifiée",
        "created_at": "2024-08-18T14:30:00.000000Z",
        "updated_at": "2024-08-18T14:30:00.000000Z",
        "profit_margin": 40.0,
        "is_profitable": true,
        "weight_display": "25.5g",
        "buy_price_formatted": "€450.00",
        "resale_price_formatted": "€750.00",
        "benefit_formatted": "€300.00"
    }
}
```

### Validation Error (422)
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "appointment_id": ["The appointment id field is required."],
        "status": ["The status field is required."],
        "item_type": ["The item type field is required."]
    }
}
```

## Field Validation Rules

| Field | Type | Required | Rules |
|-------|------|----------|-------|
| `appointment_id` | integer | Yes | Must exist in appointments table |
| `status` | string | Yes | Must be 'purchased' or 'not_purchased' |
| `description` | string | No | Free text description |
| `item_type` | string | Yes | Type of item being evaluated |
| `weight` | decimal | No | Must be >= 0 |
| `buy_price` | decimal | No | Must be >= 0 |
| `resale_price` | decimal | No | Must be >= 0 |
| `benefit` | decimal | No | Can be negative |
| `notes` | string | No | Additional notes |

## Best Practices

1. **Always validate appointment exists** before creating purchase
2. **Calculate benefit automatically** when possible: `resale_price - buy_price`
3. **Handle null values** for declined purchases
4. **Store detailed notes** for audit trail
5. **Use proper error handling** for network issues
6. **Validate numeric inputs** before sending to API
