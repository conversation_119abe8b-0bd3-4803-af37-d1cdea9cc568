<?php


// ? Assistant Routes

use App\Http\Controllers\RepresentativeController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified', 'activated', 'role:representative'])->group(function () {
    Route::get('representative/dashboard', [RepresentativeController::class, 'dashboardPage']);
    Route::get('representative/calendar', [RepresentativeController::class, 'calendarPage']);
    Route::get('representative/performance', [RepresentativeController::class, 'objectifsPage']);
    Route::get('representative/history', [RepresentativeController::class, 'historyPage']);

    // Specific route for updating purchases
    Route::put('representative/purchase/{purchase}', [RepresentativeController::class, 'update'])->name('representative.purchase.update');

    Route::resource("representative", RepresentativeController::class);
});
