<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'appointment_id' => $this->appointment_id,
            'amount' => $this->amount ? (float) $this->amount : null,
            'type' => $this->type,
            'reason' => $this->reason,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Relationships
            'user' => new UserResource($this->whenLoaded('user')),
            'appointment' => new AppointmentResource($this->whenLoaded('appointment')),
            
            // Computed fields for mobile app convenience
            'amount_formatted' => $this->when(
                $this->amount,
                function () {
                    return '€' . number_format($this->amount, 2);
                }
            ),
            'is_positive' => $this->when(
                $this->amount !== null,
                function () {
                    return $this->amount > 0;
                }
            ),
        ];
    }
}
