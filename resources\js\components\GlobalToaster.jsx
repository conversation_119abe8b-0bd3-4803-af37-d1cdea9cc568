import { usePage } from '@inertiajs/react';
import { useEffect } from 'react';

import { toast, Toaster } from 'sonner';
const GlobalToaster = () => {
    const { flash } = usePage().props;
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.error) {
            toast.error(flash.error);
        }
    }, [flash]);
    return <Toaster richColors />;
};

export default GlobalToaster;
