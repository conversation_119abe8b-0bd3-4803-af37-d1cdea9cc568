<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Purchase;
use App\Models\Bonus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PurchaseSystemTest extends TestCase
{
    use RefreshDatabase;

    public function test_weight_adjustment_for_watches()
    {
        // Create users
        $assistant = User::factory()->create(['role' => 'assistant']);
        $representative = User::factory()->create(['role' => 'representative']);

        // Create appointment
        $appointment = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $representative->id,
            'source' => 'outbound'
        ]);

        // Test data with watch category
        $requestData = [
            'appointment_id' => $appointment->id,
            'items' => [
                [
                    'type' => 'montre et montre a gousset (en argent et en or)',
                    'description' => 'Test watch',
                    'weight' => 100.0, // Original weight
                    'purchasePrice' => 500,
                    'estimatedValue' => 800,
                    'hasTransaction' => true,
                    'benefit' => 300
                ]
            ],
            'notes' => 'Test notes'
        ];

        // Act as representative and make request
        $response = $this->actingAs($representative)
            ->post(route('representative.store'), $requestData);

        // Assert purchase was created with adjusted weight (70% of original)
        $purchase = Purchase::where('appointment_id', $appointment->id)->first();
        $this->assertNotNull($purchase);
        $this->assertEquals(70.0, $purchase->weight); // 100 * 0.7 = 70
        $this->assertEquals('montre et montre a gousset (en argent et en or)', $purchase->item_type);
    }

    public function test_leboncoin_bonus_creation()
    {
        // Create users
        $assistant = User::factory()->create(['role' => 'assistant']);
        $representative = User::factory()->create(['role' => 'representative']);

        // Create appointment with leboncoin source
        $appointment = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $representative->id,
            'source' => 'leboncoin'
        ]);

        // Test data with purchased item
        $requestData = [
            'appointment_id' => $appointment->id,
            'items' => [
                [
                    'type' => 'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
                    'description' => 'Test jewelry',
                    'weight' => 50.0,
                    'purchasePrice' => 200,
                    'estimatedValue' => 400,
                    'hasTransaction' => true, // This should trigger bonus
                    'benefit' => 200
                ]
            ],
            'notes' => 'Test notes'
        ];

        // Act as representative and make request
        $response = $this->actingAs($representative)
            ->post(route('representative.store'), $requestData);

        // Assert bonus was created
        $bonus = Bonus::where('appointment_id', $appointment->id)->first();
        $this->assertNotNull($bonus);
        $this->assertEquals($assistant->id, $bonus->user_id);
        $this->assertEquals(1000, $bonus->amount); // 10.00 euros in cents
        $this->assertEquals('STANDARD', $bonus->type);
        $this->assertEquals('LBNC + Rachat', $bonus->reason);
    }

    public function test_no_bonus_for_outbound_appointments()
    {
        // Create users
        $assistant = User::factory()->create(['role' => 'assistant']);
        $representative = User::factory()->create(['role' => 'representative']);

        // Create appointment with outbound source (not leboncoin)
        $appointment = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $representative->id,
            'source' => 'outbound'
        ]);

        // Test data with purchased item
        $requestData = [
            'appointment_id' => $appointment->id,
            'items' => [
                [
                    'type' => 'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
                    'description' => 'Test jewelry',
                    'weight' => 50.0,
                    'purchasePrice' => 200,
                    'estimatedValue' => 400,
                    'hasTransaction' => true,
                    'benefit' => 200
                ]
            ],
            'notes' => 'Test notes'
        ];

        // Act as representative and make request
        $response = $this->actingAs($representative)
            ->post(route('representative.store'), $requestData);

        // Assert no bonus was created for outbound appointment
        $bonus = Bonus::where('appointment_id', $appointment->id)->first();
        $this->assertNull($bonus);
    }

    public function test_no_weight_adjustment_for_non_watch_items()
    {
        // Create users
        $assistant = User::factory()->create(['role' => 'assistant']);
        $representative = User::factory()->create(['role' => 'representative']);

        // Create appointment
        $appointment = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $representative->id,
            'source' => 'outbound'
        ]);

        // Test data with non-watch category
        $requestData = [
            'appointment_id' => $appointment->id,
            'items' => [
                [
                    'type' => 'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
                    'description' => 'Test jewelry',
                    'weight' => 100.0, // Original weight
                    'purchasePrice' => 500,
                    'estimatedValue' => 800,
                    'hasTransaction' => true,
                    'benefit' => 300
                ]
            ],
            'notes' => 'Test notes'
        ];

        // Act as representative and make request
        $response = $this->actingAs($representative)
            ->post(route('representative.store'), $requestData);

        // Assert purchase was created with original weight (no adjustment)
        $purchase = Purchase::where('appointment_id', $appointment->id)->first();
        $this->assertNotNull($purchase);
        $this->assertEquals(100.0, $purchase->weight); // No adjustment for non-watch items
    }
}
