'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { Award } from 'lucide-react';

export default function ObjectivesPage() {
    const { leaderboard } = usePage().props;

    const breadcrumbs = [
        {
            title: 'Performance',
            href: '/representative/performance',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Performance" />
            <div className="space-y-6 p-3 lg:p-6">
                <div>
                    <h1 className="text-3xl font-bold text-[#525e62]">Objectifs & Classement</h1>
                    <p className="text-[#525e62]/70">Suivez vos objectifs hebdomadaires et votre position dans l'équipe.</p>
                </div>
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    {/* Classement de l'équipe */}
                    <Card className="border-0 bg-white shadow-lg lg:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center text-[#525e62]">
                                <Award className="mr-2 h-5 w-5" />
                                Classement de l'Équipe - Cette Semaine
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {leaderboard.map((person) => (
                                    <div
                                        key={person.rank}
                                        className={`rounded-lg border-2 p-4 transition-all ${
                                            person.isCurrentUser
                                                ? 'border-[#525e62] bg-[#525e62]/5 shadow-md'
                                                : 'border-[#525e62]/10 hover:border-[#525e62]/30'
                                        }`}
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-4">
                                                <div className="flex items-center space-x-2">
                                                    <span className="text-2xl">{person.badge}</span>
                                                    <span className="text-lg font-bold text-[#525e62]">#{person.rank}</span>
                                                </div>

                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#525e62]">
                                                        <span className="text-sm font-medium text-white">{person.avatar}</span>
                                                    </div>
                                                    <div>
                                                        <p
                                                            className={`font-medium ${person.isCurrentUser ? 'font-bold text-[#525e62]' : 'text-[#525e62]'}`}
                                                        >
                                                            {person.name}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Podium visuel */}
                <Card className="border-0 bg-white shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-[#525e62]">Podium du Semaine</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-end justify-center space-x-8 py-8">
                            {/* 2ème place */}
                            {leaderboard.length > 1 && (
                                <div className="text-center">
                                    <div className="mb-4 flex h-24 w-20 items-end justify-center rounded-t-lg bg-gray-300 pb-3 shadow-lg">
                                        <span className="text-xl font-bold text-white">2</span>
                                    </div>
                                    <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-[#525e62]">
                                        <span className="font-medium text-white">{leaderboard[1].avatar}</span>
                                    </div>
                                    <p className="font-medium text-[#525e62]">{leaderboard[1].name}</p>
                                </div>
                            )}

                            {/* 1ère place */}
                            {leaderboard.length > 0 && (
                                <div className="text-center">
                                    <div className="mb-4 flex h-32 w-20 items-end justify-center rounded-t-lg bg-yellow-400 pb-3 shadow-lg">
                                        <span className="text-xl font-bold text-white">1</span>
                                    </div>
                                    <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-[#525e62]">
                                        <span className="font-medium text-white">{leaderboard[0].avatar}</span>
                                    </div>
                                    <p className="font-medium text-[#525e62]">{leaderboard[0].name}</p>
                                </div>
                            )}

                            {/* 3ème place */}
                            {leaderboard.length > 2 && (
                                <div className="text-center">
                                    <div className="mb-4 flex h-20 w-20 items-end justify-center rounded-t-lg bg-orange-400 pb-3 shadow-lg">
                                        <span className="text-xl font-bold text-white">3</span>
                                    </div>
                                    <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-[#525e62]">
                                        <span className="font-medium text-white">{leaderboard[2].avatar}</span>
                                    </div>
                                    <p className="font-medium text-[#525e62]">{leaderboard[2].name}</p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
