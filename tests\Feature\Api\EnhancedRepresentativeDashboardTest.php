<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Purchase;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class EnhancedRepresentativeDashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set a fixed date for consistent testing
        Carbon::setTestNow(Carbon::parse('2024-08-22 10:00:00'));
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow(); // Reset Carbon
        parent::tearDown();
    }

    /** @test */
    public function it_returns_enhanced_dashboard_stats_for_representative()
    {
        // Create representative and assistant
        $representative = User::factory()->create(['role' => 'representative']);
        $assistant = User::factory()->create(['role' => 'assistant']);
        Sanctum::actingAs($representative);

        $today = Carbon::today();
        $yesterday = Carbon::yesterday();

        // Create today's appointments
        $todayApp1 = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $representative->id,
            'dateTime' => $today->copy()->addHours(10),
        ]);

        $todayApp2 = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $representative->id,
            'dateTime' => $today->copy()->addHours(14),
        ]);

        // Create another appointment this week for weekly stats
        $thisWeekApp = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $representative->id,
            'dateTime' => $today->copy()->subDays(2)->addHours(10), // Tuesday this week
        ]);

        // Create purchases - today's completed appointment
        Purchase::factory()->create([
            'appointment_id' => $todayApp1->id,
            'status' => 'purchased',
            'buy_price' => 100.00,
            'resale_price' => 150.00,
            'benefit' => 50.00,
        ]);

        // Create purchases - this week's completed appointment
        Purchase::factory()->create([
            'appointment_id' => $thisWeekApp->id,
            'status' => 'purchased',
            'buy_price' => 200.00,
            'resale_price' => 300.00,
            'benefit' => 100.00,
        ]);

        // Make the API request
        $response = $this->getJson('/api/v1/appointments/representative-dashboard-stats');

        // Assert the response structure
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'today' => [
                            'appointments',
                            'completed_appointments',
                            'conversion_rate',
                            'revenue',
                            'total_cost',
                            'benefit',
                            'profit_margin',
                            'date'
                        ],
                        'weekly' => [
                            'total_appointments',
                            'completed_appointments',
                            'conversion_rate',
                            'total_revenue',
                            'total_cost',
                            'total_benefit',
                            'profit_margin',
                            'week_start',
                            'week_end',
                            'week_number',
                            'year'
                        ],
                        // Legacy fields
                        'todays_appointments',
                        'todays_realized_appointments',
                        'todays_revenue',
                        'todays_benefit',
                        'realization_rate',
                        'date'
                    ]
                ]);

        // Assert specific values
        $data = $response->json('data');
        
        // Today's stats
        $this->assertEquals(2, $data['today']['appointments']);
        $this->assertEquals(1, $data['today']['completed_appointments']);
        $this->assertEquals(50.0, $data['today']['conversion_rate']);
        $this->assertEquals(150.0, $data['today']['revenue']);
        $this->assertEquals(100.0, $data['today']['total_cost']);
        $this->assertEquals(50.0, $data['today']['benefit']);
        $this->assertEquals(33.3, $data['today']['profit_margin']);

        // Weekly stats (includes today's appointments + this week's appointment)
        $this->assertEquals(3, $data['weekly']['total_appointments']);
        $this->assertEquals(2, $data['weekly']['completed_appointments']);
        $this->assertEquals(66.7, $data['weekly']['conversion_rate']);
        $this->assertEquals(450.0, $data['weekly']['total_revenue']);
        $this->assertEquals(300.0, $data['weekly']['total_cost']);
        $this->assertEquals(150.0, $data['weekly']['total_benefit']);
        $this->assertEquals(33.3, $data['weekly']['profit_margin']);

        // Legacy fields
        $this->assertEquals(2, $data['todays_appointments']);
        $this->assertEquals(1, $data['todays_realized_appointments']);
        $this->assertEquals(150.0, $data['todays_revenue']);
        $this->assertEquals(50.0, $data['todays_benefit']);
        $this->assertEquals(50.0, $data['realization_rate']);
    }

    /** @test */
    public function it_handles_zero_appointments_gracefully()
    {
        $representative = User::factory()->create(['role' => 'representative']);
        Sanctum::actingAs($representative);

        $response = $this->getJson('/api/v1/appointments/representative-dashboard-stats');

        $response->assertStatus(200);
        $data = $response->json('data');

        // All values should be 0 when no appointments exist
        $this->assertEquals(0, $data['today']['appointments']);
        $this->assertEquals(0, $data['today']['completed_appointments']);
        $this->assertEquals(0, $data['today']['conversion_rate']);
        $this->assertEquals(0, $data['today']['profit_margin']);
        $this->assertEquals(0, $data['weekly']['total_appointments']);
        $this->assertEquals(0, $data['weekly']['conversion_rate']);
        $this->assertEquals(0, $data['weekly']['profit_margin']);
    }

    /** @test */
    public function it_requires_representative_role()
    {
        $assistant = User::factory()->create(['role' => 'assistant']);
        Sanctum::actingAs($assistant);

        $response = $this->getJson('/api/v1/appointments/representative-dashboard-stats');

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Only representatives can access dashboard stats'
                ]);
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->getJson('/api/v1/appointments/representative-dashboard-stats');
        
        $response->assertStatus(401);
    }
}
