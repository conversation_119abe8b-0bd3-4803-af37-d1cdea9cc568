# RDV (Rendez-vous) API Endpoint

## Overview
The RDV endpoint is a specialized appointment creation endpoint designed specifically for mobile applications. It provides enhanced validation, business logic, and mobile-optimized features for creating appointments.

## Endpoint Details

**URL:** `POST /api/v1/appointments/rdv`  
**Authentication:** Required (<PERSON><PERSON>)  
**Role:** Assistant only  

## Request Format

### Headers
```
Authorization: Bearer {your_token}
Content-Type: application/json
```

### Request Body
```json
{
    "representative_id": 123,
    "client_name": "<PERSON>",
    "client_phone": "0123456789",
    "client_address": "123 Rue de la Paix, 75001 Paris",
    "source": "outbound",
    "date_time": "2024-08-20T14:30:00.000Z",
    "notes": "Client intéressé par la vente de bijoux anciens",
    "items_collection": [
        "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
        "montres"
    ],
    "appointment_type": "announced"
}
```

### Field Validation

| Field | Type | Required | Validation Rules |
|-------|------|----------|------------------|
| `representative_id` | integer | Yes | Must exist in users table with role 'representative' |
| `client_name` | string | Yes | Max 255 characters |
| `client_phone` | string | Yes | Max 20 characters |
| `client_address` | string | Yes | Max 500 characters |
| `source` | string | Yes | Must be 'outbound' or 'leboncoin' |
| `date_time` | datetime | Yes | Must be in the future (ISO 8601 format) |
| `notes` | string | No | Max 1000 characters |
| `items_collection` | array | Yes | At least 1 item, each item must be a string |
| `appointment_type` | string | Yes | Must be 'announced' or 'not_announced' |

## Business Logic

### Access Control
1. **Role Verification**: Only users with role 'assistant' can create RDV
2. **Assignment Check**: Assistant must be assigned to the selected representative
3. **Representative Status**: Selected representative must be activated

### Automatic Fields
- `assistant_id`: Automatically set to the authenticated user's ID
- `created_at` / `updated_at`: Automatically managed by Laravel

## Response Format

### Success Response (201 Created)
```json
{
    "success": true,
    "message": "RDV created successfully",
    "data": {
        "id": 456,
        "assistant_id": 123,
        "representative_id": 789,
        "client_name": "Jean Dupont",
        "client_phone": "0123456789",
        "client_address": "123 Rue de la Paix, 75001 Paris",
        "source": "outbound",
        "date_time": "2024-08-20T14:30:00.000000Z",
        "notes": "Client intéressé par la vente de bijoux anciens",
        "items_collection": [
            "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
            "montres"
        ],
        "appointment_type": "announced",
        "created_at": "2024-08-18T10:15:30.000000Z",
        "updated_at": "2024-08-18T10:15:30.000000Z",
        "assistant": {
            "id": 123,
            "name": "Assistant Name",
            "email": "<EMAIL>",
            "role": "assistant"
        },
        "representative": {
            "id": 789,
            "name": "Representative Name",
            "email": "<EMAIL>",
            "role": "representative"
        }
    }
}
```

### Error Responses

#### Validation Error (422)
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "client_name": ["The client name field is required."],
        "date_time": ["The date time must be a date after now."]
    }
}
```

#### Forbidden - Wrong Role (403)
```json
{
    "success": false,
    "message": "Only assistants can create appointments"
}
```

#### Forbidden - Not Assigned (403)
```json
{
    "success": false,
    "message": "You are not assigned to this representative"
}
```

#### Bad Request - Invalid Representative (400)
```json
{
    "success": false,
    "message": "Selected user is not a representative"
}
```

#### Bad Request - Inactive Representative (400)
```json
{
    "success": false,
    "message": "Selected representative is not activated"
}
```

## Usage Examples

### Mobile App Integration

```javascript
// Example using fetch API
const createRDV = async (rdvData, token) => {
    try {
        const response = await fetch('/api/v1/appointments/rdv', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(rdvData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('RDV created:', result.data);
            return result.data;
        } else {
            console.error('Error:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Network error:', error);
        throw error;
    }
};
```

### React Native Example

```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';

const createRDV = async (rdvData) => {
    const token = await AsyncStorage.getItem('auth_token');
    
    const response = await fetch(`${API_BASE_URL}/api/v1/appointments/rdv`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(rdvData)
    });
    
    return await response.json();
};
```

## Differences from Standard Appointment Creation

| Feature | Standard `/appointments` | RDV `/appointments/rdv` |
|---------|-------------------------|-------------------------|
| Role Access | Any authenticated user | Assistant only |
| Assignment Check | No | Yes - must be assigned to representative |
| Representative Validation | Basic existence check | Role + activation status check |
| Error Messages | Generic | Mobile-optimized, specific |
| Field Validation | Basic | Enhanced with mobile considerations |
| Response Format | Standard | Includes related user data |

## Testing

The endpoint includes comprehensive tests covering:
- Successful RDV creation by assistant
- Assignment validation
- Role-based access control
- Validation error handling
- Representative status verification

Run tests with:
```bash
php artisan test --filter rdv
```
