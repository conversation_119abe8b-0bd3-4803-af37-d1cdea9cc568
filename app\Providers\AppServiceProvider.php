<?php

namespace App\Providers;

use App\Models\Appointment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;
use Inertia\Inertia;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Inertia::share([

            // ? Weekly assistant data
            // & Total This Week
            "weeklyAppointmentsTotal" => function () {
                $start = Carbon::now()->startOfWeek();
                $end = Carbon::now()->endOfWeek();

                return count(Appointment::where('assistant_id', Auth::id())
                    ->whereBetween("created_at", [$start, $end])
                    ->get());
            },

            // & Total Annonced This Week
            "weeklyAnnouncedAppointmentsTotal" => function () {
                $start = Carbon::now()->startOfWeek();
                $end = Carbon::now()->endOfWeek();

                return count(Appointment::where('assistant_id', Auth::id())
                    ->whereBetween("created_at", [$start, $end])
                    ->where('appointment_type', "announced")
                    ->get());
            },

            // & Total This Day
            "dailyAppointments" => function () {
                $today = Carbon::now()->today();
                return count(Appointment::where('assistant_id', Auth::id())
                    ->whereDate("created_at", $today)
                    ->get());
            },

            // & Total Annonced This Day
            "announcedTodayAppointments" => function () {
                $today = Carbon::now()->today();
                return count(Appointment::where('assistant_id', Auth::id())
                    ->whereDate("created_at", $today)
                    ->where('appointment_type', "announced")
                    ->get());
            },

            // & Assistants Leaderboard
            "assistantsLeaderboard" => function () {

                $assistantsLeaderboard = User::where('role', 'assistant')
                    ->withSum(['bonuses as total_bonus' => function ($query) {
                        $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    }], 'amount') // 'amount' is the column to sum
                    ->orderByDesc('total_bonus')
                    ->get();
                return $assistantsLeaderboard;
            }
        ]);
    }
}
