<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assistant_representative_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assistant_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('representative_id')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Ensure unique pairing
            $table->unique(['assistant_id', 'representative_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assistant_representative_assignments');
    }
};
