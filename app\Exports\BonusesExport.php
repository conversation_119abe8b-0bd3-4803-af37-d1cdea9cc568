<?php

namespace App\Exports;

use App\Models\Bonus;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BonusesExport implements FromQuery, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    public function __construct(public $month, public $year)
    {
        $this->month = $month;
        $this->year = $year;
    }
    public function query()
    {
        return Bonus::query()
            ->leftJoin('users as assistants', 'bonuses.user_id', '=', 'assistants.id')
            ->select(
                'assistants.name as assistant_name',
                'bonuses.amount',
                'bonuses.type',
                'bonuses.reason',
                'bonuses.created_at as dateTime',
            )
            ->whereYear('bonuses.created_at', $this->year)
            ->whereMonth('bonuses.created_at', $this->month);
    }
    public function headings(): array
    {
        return [
            'Assistant',
            'Montant',
            'Type',
            'raison',
            'Date',
        ];
    }
    public function map($row): array
    {
        return [
            $row->assistant_name,
            $row->amount,
            $row->type === 'daily_sub' ? 'rendez-vous quotidienne' : $row->type,
            $row->reason,
            $row->dateTime,
        ];
    }
    public function styles(Worksheet $sheet)
    {
        // Color the heading row (A1:J1) with a light blue background
        $sheet->getStyle('A1:J1')->applyFromArray([
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9E1F2'],
            ],
            'font' => [
                'bold' => true,
            ],
        ]);
    }
}
