# Representative Dashboard Stats API

This endpoint provides comprehensive statistics for representatives to display on their mobile dashboard, including today's performance and overall metrics.

## Important Changes to Appointments Endpoints

### Updated Behavior for Appointments List
- **`GET /api/v1/appointments`** and **`GET /api/v1/appointments/my-appointments`** now return only **upcoming appointments** by default
- To include past appointments, add the query parameter `include_past=true`
- All appointments now include purchase relationships to check appointment status
- Appointments are ordered by date (upcoming first)

### Appointment Status Field
Each appointment now includes a `status` field that indicates:
- `"pending"`: No purchases exist for this appointment
- `"completed"`: Appointment has purchases with status 'purchased'
- `"no_purchase"`: Appointment has purchases but none with status 'purchased'

## Dashboard Stats Endpoint

```
GET /api/v1/appointments/representative-dashboard-stats
```

## Authentication

- **Required**: Yes
- **Type**: Bear<PERSON>ken (Sanctum)
- **Role**: Only `representative` users can access this endpoint

## Request

### Headers
```
Authorization: Bearer {token}
Content-Type: application/json
```

### Parameters
None required. The endpoint automatically calculates stats for the current authenticated representative for today's date.

## Response

### Success Response (200)

```json
{
    "success": true,
    "message": "Representative dashboard stats retrieved successfully",
    "data": {
        "today": {
            "appointments": 5,
            "completed_appointments": 3,
            "conversion_rate": 60.0,
            "revenue": 450.75,
            "total_cost": 300.50,
            "benefit": 150.25,
            "profit_margin": 33.4,
            "date": "2025-08-20"
        },
        "weekly": {
            "total_appointments": 12,
            "completed_appointments": 8,
            "conversion_rate": 66.7,
            "total_revenue": 1250.75,
            "total_cost": 850.50,
            "total_benefit": 400.25,
            "profit_margin": 32.0,
            "week_start": "2025-08-18",
            "week_end": "2025-08-24",
            "week_number": 34,
            "year": 2025
        },
        "todays_appointments": 5,
        "todays_realized_appointments": 3,
        "todays_revenue": 450.75,
        "todays_benefit": 150.25,
        "realization_rate": 60.0,
        "date": "2025-08-20"
    }
}
```

### Response Fields

#### Today's Performance (`today` object)
| Field | Type | Description |
|-------|------|-------------|
| `appointments` | integer | Total appointments scheduled for today |
| `completed_appointments` | integer | Appointments that resulted in purchases today |
| `conversion_rate` | float | Percentage of today's appointments converted (0-100) |
| `revenue` | float | Total revenue from today's purchases |
| `total_cost` | float | Total cost (buy prices) from today's purchases |
| `benefit` | float | Total profit/benefit from today's purchases |
| `profit_margin` | float | Profit margin percentage for today (benefit/revenue * 100) |
| `date` | string | Date for today's stats (YYYY-MM-DD) |

#### Weekly Performance (`weekly` object)
| Field | Type | Description |
|-------|------|-------------|
| `total_appointments` | integer | Total appointments scheduled this week |
| `completed_appointments` | integer | Appointments that resulted in purchases this week |
| `conversion_rate` | float | Weekly conversion rate percentage (0-100) |
| `total_revenue` | float | Total revenue from this week's completed appointments |
| `total_cost` | float | Total cost (buy prices) from this week's purchases |
| `total_benefit` | float | Total profit/benefit from this week's completed appointments |
| `profit_margin` | float | Weekly profit margin percentage (benefit/revenue * 100) |
| `week_start` | string | Start date of the week (Monday, YYYY-MM-DD) |
| `week_end` | string | End date of the week (Sunday, YYYY-MM-DD) |
| `week_number` | integer | Week number of the year (1-53) |
| `year` | integer | Year of the week |

#### Legacy Fields (for backward compatibility)
| Field | Type | Description |
|-------|------|-------------|
| `todays_appointments` | integer | Same as `today.appointments` |
| `todays_realized_appointments` | integer | Same as `today.completed_appointments` |
| `todays_revenue` | float | Same as `today.revenue` |
| `todays_benefit` | float | Same as `today.benefit` |
| `realization_rate` | float | Same as `today.conversion_rate` |
| `date` | string | Same as `today.date` |

### Error Responses

#### 401 Unauthorized
```json
{
    "message": "Unauthenticated."
}
```

#### 403 Forbidden
```json
{
    "success": false,
    "message": "Only representatives can access dashboard stats"
}
```

## Usage Examples

### cURL Example
```bash
curl -X GET \
  'https://your-domain.com/api/v1/appointments/representative-dashboard-stats' \
  -H 'Authorization: Bearer your-token-here' \
  -H 'Content-Type: application/json'
```

### JavaScript/Fetch Example
```javascript
const response = await fetch('/api/v1/appointments/representative-dashboard-stats', {
    method: 'GET',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    }
});

const data = await response.json();
console.log(data.data); // Dashboard stats
```

### React Native Example
```javascript
const getDashboardStats = async () => {
    try {
        const response = await fetch('/api/v1/appointments/representative-dashboard-stats', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            setDashboardStats(result.data);
        }
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
    }
};
```

## Business Logic

### Today's Performance Calculations
1. **Today's Appointments**: Counts all appointments where `dateTime` is today and `representative_id` matches the authenticated user
2. **Completed Appointments**: Counts appointments that have at least one purchase with `status = 'purchased'` for today
3. **Conversion Rate**: Calculated as `(completed_appointments / total_appointments) * 100`
4. **Revenue**: Sums the `resale_price` from all purchases with `status = 'purchased'` for today's appointments
5. **Total Cost**: Sums the `buy_price` from all purchases with `status = 'purchased'` for today's appointments
6. **Benefit**: Sums the `benefit` field from all purchases with `status = 'purchased'` for today's appointments
7. **Profit Margin**: Calculated as `(benefit / revenue) * 100`

### Weekly Performance Calculations
1. **Weekly Appointments**: Counts all appointments scheduled for the current week (Monday to Sunday)
2. **Weekly Completed Appointments**: Counts appointments that have at least one purchase with `status = 'purchased'` for this week
3. **Weekly Conversion Rate**: Calculated as `(weekly_completed_appointments / weekly_appointments) * 100`
4. **Weekly Revenue**: Sums all `resale_price` from purchases with `status = 'purchased'` for this week's appointments
5. **Weekly Total Cost**: Sums all `buy_price` from purchases with `status = 'purchased'` for this week's appointments
6. **Weekly Benefit**: Sums all `benefit` from purchases with `status = 'purchased'` for this week's appointments
7. **Weekly Profit Margin**: Calculated as `(weekly_benefit / weekly_revenue) * 100`
8. **Week Period**: Current week from Monday to Sunday, includes week number and year

### Key Metrics Explained
- **Conversion Rate**: Percentage of appointments that result in actual purchases
- **Profit Margin**: Percentage of revenue that becomes profit (higher is better)
- **Benefit**: Actual profit amount (resale_price - buy_price)
- **Revenue**: Total sales amount (resale_price)

## Mobile App Integration Example

Here's how you might integrate this endpoint into a React Native dashboard component:

```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, RefreshControl, ScrollView } from 'react-native';

const RepresentativeDashboard = ({ userToken }) => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);

    const fetchDashboardStats = async () => {
        try {
            const response = await fetch('/api/v1/appointments/representative-dashboard-stats', {
                headers: {
                    'Authorization': `Bearer ${userToken}`,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();
            if (result.success) {
                setStats(result.data);
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchDashboardStats();
    }, []);

    const onRefresh = () => {
        setRefreshing(true);
        fetchDashboardStats();
    };

    if (loading) return <Text>Loading...</Text>;

    return (
        <ScrollView
            style={styles.container}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        >
            <Text style={styles.title}>Today's Performance</Text>

            <View style={styles.statsContainer}>
                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>{stats?.todays_appointments || 0}</Text>
                    <Text style={styles.statLabel}>Appointments</Text>
                </View>

                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>{stats?.todays_realized_appointments || 0}</Text>
                    <Text style={styles.statLabel}>Realized</Text>
                </View>

                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>€{stats?.todays_revenue?.toFixed(2) || '0.00'}</Text>
                    <Text style={styles.statLabel}>Revenue</Text>
                </View>

                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>{stats?.realization_rate?.toFixed(1) || 0}%</Text>
                    <Text style={styles.statLabel}>Success Rate</Text>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, padding: 16 },
    title: { fontSize: 24, fontWeight: 'bold', marginBottom: 16 },
    statsContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' },
    statCard: { width: '48%', backgroundColor: '#f5f5f5', padding: 16, borderRadius: 8, marginBottom: 16 },
    statNumber: { fontSize: 24, fontWeight: 'bold', color: '#333' },
    statLabel: { fontSize: 14, color: '#666', marginTop: 4 }
});
```

## Notes

- All monetary values are returned as floats
- The `date` field shows the date for which statistics are calculated (always today)
- If no appointments exist for today, all counts will be 0
- The realization rate is rounded to 1 decimal place
- Only appointments scheduled for today (based on `dateTime` field) are included in calculations
- Consider implementing pull-to-refresh functionality for real-time updates
- Cache the data appropriately to avoid excessive API calls

## Updated Appointments Endpoints

### Get Appointments List
```
GET /api/v1/appointments
GET /api/v1/appointments/my-appointments
```

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `include_past` | boolean | `false` | Include past appointments in the results |
| `per_page` | integer | `15` | Number of results per page |
| `assistant_id` | integer | - | Filter by assistant ID |
| `representative_id` | integer | - | Filter by representative ID |
| `source` | string | - | Filter by source (`outbound`, `leboncoin`) |
| `appointment_type` | string | - | Filter by type (`announced`, `not_announced`) |
| `date_from` | date | - | Filter appointments from this date |
| `date_to` | date | - | Filter appointments to this date |

#### Examples

**Get upcoming appointments only (default):**
```javascript
fetch('/api/v1/appointments/my-appointments', {
    headers: { 'Authorization': `Bearer ${token}` }
});
```

**Get all appointments including past ones:**
```javascript
fetch('/api/v1/appointments/my-appointments?include_past=true', {
    headers: { 'Authorization': `Bearer ${token}` }
});
```

#### Response Structure
```json
{
    "success": true,
    "message": "User appointments retrieved successfully",
    "data": {
        "data": [
            {
                "id": 1,
                "client_name": "John Doe",
                "date_time": "2025-08-21T10:00:00.000Z",
                "status": "pending",
                "purchases": [],
                "assistant": {...},
                "representative": {...}
            }
        ],
        "pagination": {...}
    }
}
```

### Appointment Status Values
- **`"pending"`**: No purchases recorded yet
- **`"completed"`**: Has successful purchases (status = 'purchased')
- **`"no_purchase"`**: Has purchase records but no successful purchases
