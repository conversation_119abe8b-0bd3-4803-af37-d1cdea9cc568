<?php

use App\Http\Controllers\AppointmentController;
use App\Models\User;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth', 'verified', 'activated', 'role:assistant'])->group(function () {

    // ? Pages
    Route::inertia('/welcomeAssistant', 'AssistantPages/welcomeAssistant');
    Route::get('/appointmentCreation', [AppointmentController::class, 'getReps']);
    Route::inertia('/appointmentsHistory', 'AssistantPages/appointmentsHistory');
    // Route::inertia('/assistantPerformance', 'AssistantPages/assistantPerformance');
    Route::get('/assistantPerformance',[AppointmentController::class,'performencePage'] );

    // ? Functions

    Route::get('/appointmentsHistory/by-date', [AppointmentController::class, 'byDate'])->name('appointments.byDate');
    Route::resource("appointments", AppointmentController::class);
});
