<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assistant_id')->constrained("users")->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('representative_id')->constrained("users")->cascadeOnUpdate()->cascadeOnDelete();
            $table->string('clientName');
            $table->string('clientPhone');
            $table->string('clientAddress');
            $table->enum('source', ['outbound', 'leboncoin']);
            $table->dateTime('dateTime');
            $table->text('notes')->nullable();
            $table->json('itemsCollection');
            $table->enum('appointment_type', ['announced', 'not_announced'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
