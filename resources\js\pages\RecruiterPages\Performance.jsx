import { useState } from 'react';
import { Head, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    TrendingUp,
    Users,
    Target,
    BarChart3,
    Calendar,
    Award,
    Activity
} from 'lucide-react';
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    BarChart,
    Bar
} from 'recharts';

export default function Performance() {
    const { assistantsData, representativesData, performanceTrends } = usePage().props;
    

    const MetricCard = ({ title, value, subtitle, icon: Icon, color = "text-[#525e62]" }) => (
        <Card className="border-[#525e62]/10">
            <CardContent className="p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="text-sm font-medium text-[#525e62]/70">{title}</p>
                        <p className={`text-2xl font-bold ${color}`}>{value}</p>
                        {subtitle && <p className="text-xs text-[#525e62]/50">{subtitle}</p>}
                    </div>
                    <div className="h-12 w-12 rounded-lg bg-[#525e62]/10 flex items-center justify-center">
                        <Icon className="h-6 w-6 text-[#525e62]" />
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout>
            <Head title="Analyse des Performances" />
            
            <div className="min-h-screen bg-white p-6">
                <div className="max-w-7xl mx-auto space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-[#525e62]">Analyse des Performances</h1>
                            <p className="text-[#525e62]/70">
                                Analyse détaillée des performances d'équipe
                            </p>
                        </div>
                    </div>

                    {/* Key Performance Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <MetricCard
                            title="Total Assistants"
                            value={assistantsData?.total_assistants || 0}
                            subtitle="Actifs cette semaine"
                            icon={Users}
                        />
                        <MetricCard
                            title="RDV Validés"
                            value={assistantsData?.total_validated || 0}
                            subtitle={`Sur ${assistantsData?.total_appointments || 0} RDV`}
                            icon={Calendar}
                        />
                        <MetricCard
                            title="Total Représentants"
                            value={representativesData?.total_representatives || 0}
                            subtitle="Actifs cette semaine"
                            icon={Target}
                        />
                        <MetricCard
                            title="CA Total"
                            value={`${(representativesData?.total_revenue || 0).toLocaleString()} €`}
                            subtitle={`${representativesData?.total_purchases || 0} achats`}
                            icon={TrendingUp}
                            color="text-green-600"
                        />
                    </div>

                    {/* Performance Charts */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Assistant Performance */}
                        <Card className="border-[#525e62]/10">
                            <CardHeader>
                                <CardTitle className="text-[#525e62] flex items-center">
                                    <Users className="h-5 w-5 mr-2" />
                                    Top Assistants
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {assistantsData?.leaderboard?.slice(0, 5).map((assistant, index) => (
                                        <div key={assistant.id} className="flex items-center justify-between p-3 rounded-lg bg-[#f1efe0]/30">
                                            <div className="flex items-center space-x-3">
                                                <div className="h-8 w-8 rounded-full bg-[#525e62] flex items-center justify-center text-white text-sm font-medium">
                                                    {assistant.avatar}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-[#525e62]">{assistant.name}</p>
                                                    <p className="text-sm text-[#525e62]/70">
                                                        {assistant.validated_appointments} RDV validés
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-bold text-[#525e62]">{assistant.validation_rate}%</p>
                                                <Badge variant="outline" className="text-xs">#{index + 1}</Badge>
                                            </div>
                                        </div>
                                    )) || <p className="text-[#525e62]/70">Aucune donnée disponible</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Representative Performance */}
                        <Card className="border-[#525e62]/10">
                            <CardHeader>
                                <CardTitle className="text-[#525e62] flex items-center">
                                    <Target className="h-5 w-5 mr-2" />
                                    Top Représentants
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {representativesData?.leaderboard?.slice(0, 5).map((rep, index) => (
                                        <div key={rep.id} className="flex items-center justify-between p-3 rounded-lg bg-[#f1efe0]/30">
                                            <div className="flex items-center space-x-3">
                                                <div className="h-8 w-8 rounded-full bg-[#8b9dc3] flex items-center justify-center text-white text-sm font-medium">
                                                    {rep.avatar}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-[#525e62]">{rep.name}</p>
                                                    <p className="text-sm text-[#525e62]/70">
                                                        {rep.total_purchases} achats
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-bold text-green-600">
                                                    {(rep.total_revenue || 0).toLocaleString()} €
                                                </p>
                                                <Badge variant="outline" className="text-xs">#{index + 1}</Badge>
                                            </div>
                                        </div>
                                    )) || <p className="text-[#525e62]/70">Aucune donnée disponible</p>}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Performance Trends Chart */}
                    {performanceTrends && performanceTrends.length > 0 && (
                        <Card className="border-[#525e62]/10">
                            <CardHeader>
                                <CardTitle className="text-[#525e62] flex items-center">
                                    <BarChart3 className="h-5 w-5 mr-2" />
                                    Évolution des Performances (4 dernières semaines)
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={300}>
                                    <LineChart data={performanceTrends}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#525e62/20" />
                                        <XAxis dataKey="week" stroke="#525e62" />
                                        <YAxis stroke="#525e62" />
                                        <Tooltip
                                            contentStyle={{
                                                backgroundColor: '#f1efe0',
                                                border: '1px solid #525e62',
                                                borderRadius: '8px'
                                            }}
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="assistant_appointments"
                                            stroke="#525e62"
                                            strokeWidth={3}
                                            name="RDV Assistants"
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="rep_revenue"
                                            stroke="#8b9dc3"
                                            strokeWidth={3}
                                            name="CA Représentants"
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>
                    )}

                    
                </div>
            </div>
        </AppLayout>
    );
}
