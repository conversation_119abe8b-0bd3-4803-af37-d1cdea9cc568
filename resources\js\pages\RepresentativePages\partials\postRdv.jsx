'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from '@inertiajs/react';
import { Calculator, Save } from 'lucide-react';
import { useEffect, useState } from 'react';

export function PostRDVModal({ isOpen, onClose, appointment, next }) {
    const [calculatedProfit, setCalculatedProfit] = useState(0);
    const [profitMargin, setProfitMargin] = useState(0);
    const [formData, setFormData] = useState({
        items: [],
        totalPurchasePrice: '',
        totalEstimatedValue: '',
        notes: '',
    });

    // Initialize items based on appointment's itemsCollection
    useEffect(() => {
        if (appointment?.itemsCollection) {
            const initialItems = appointment.itemsCollection.map((itemType) => ({
                type: itemType,
                description: '',
                weight: '',
                purity: '',
                condition: '',
                purchasePrice: '',
                estimatedValue: '',
                hasTransaction: false, // Individual transaction status
                benefit: 0, // Individual benefit calculation
            }));
            setFormData(prev => ({
                ...prev,
                items: initialItems
            }));
        }
    }, [appointment]);

    const { data, setData, post, processing, errors } = useForm({
        appointment_id: appointment?.id,
        items: formData.items,
        notes: formData.notes,
    });

    const calculateTotals = () => {
        // Calculate individual benefits and update items
        const updatedItems = formData.items.map(item => {
            const purchasePrice = Number.parseFloat(item.purchasePrice) || 0;
            const estimatedValue = Number.parseFloat(item.estimatedValue) || 0;
            const itemBenefit = estimatedValue - purchasePrice;
            return {
                ...item,
                benefit: itemBenefit
            };
        });

        // Only include items that are purchased (hasTransaction === true)
        const purchasedItems = updatedItems.filter(item => item.hasTransaction);

        // Calculate totals
        const totalPurchase = purchasedItems.reduce((sum, item) => {
            return sum + (Number.parseFloat(item.purchasePrice) || 0);
        }, 0);

        const totalEstimated = purchasedItems.reduce((sum, item) => {
            return sum + (Number.parseFloat(item.estimatedValue) || 0);
        }, 0);

        const totalProfit = purchasedItems.reduce((sum, item) => {
            return sum + item.benefit;
        }, 0);

        const margin = totalPurchase > 0 ? (totalProfit / totalPurchase) * 100 : 0;

        setCalculatedProfit(totalProfit);
        setProfitMargin(margin);
        setFormData((prev) => ({
            ...prev,
            items: updatedItems,
            totalPurchasePrice: totalPurchase.toString(),
            totalEstimatedValue: totalEstimated.toString(),
        }));
    };


    const removeItem = (index) => {
        setFormData((prev) => ({
            ...prev,
            items: prev.items.filter((_, i) => i !== index),
        }));
    };

    // Call calculateTotals automatically when items change
    // useEffect(() => {
    //     calculateTotals();
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [formData.items]);

    const updateItem = (itemIndex, field, value) => {
        setFormData((prev) => {
            const newItems = prev.items.map((item, index) =>
                index === itemIndex ? { ...item, [field]: value } : item
            );
            return {
                ...prev,
                items: newItems,
            };
        });
    };

    const updateGlobalField = (field, value) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };
    useEffect(() => {
        setData({
            appointment_id: appointment?.id,
            items: formData.items,
            notes: formData.notes,
        });
    }, [
        appointment?.id,
        formData.items,
        formData.notes,
        setData,
    ]);

    const handleSubmit = () => {
        calculateTotals();
        // console.log('Post-RDV data:', data);
        // Ici on sauvegarderait les données
        post(route('representative.store'), {
            onSuccess: () => {
                // Clear the form data after successful submit
                const initialItems = appointment.itemsCollection.map((itemType) => ({
                    type: itemType,
                    description: '',
                    weight: '',
                    purity: '',
                    condition: '',
                    purchasePrice: '',
                    estimatedValue: '',
                    hasTransaction: false,
                    benefit: 0,
                }));
                next();
                setFormData({
                    items: initialItems,
                    totalPurchasePrice: '',
                    totalEstimatedValue: '',
                    notes: '',
                });
                setData({
                    appointment_id: appointment?.id,
                    items: initialItems,
                    notes: '',
                });
                setProfitMargin(0);
                setCalculatedProfit(0);
                onClose();
            },
        });
        console.log(data);
        onClose();
    };
    console.log(appointment.itemsCollection)
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center text-[#525e62]">Fiche Post-RDV - {appointment.clientName}</DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    {/* Informations du RDV */}
                    <Card className="bg-[#f1efe0]/30">
                        <CardHeader>
                            <CardTitle className="text-lg text-[#525e62]">Informations du Rendez-vous</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-3">
                                <div>
                                    <span className="text-[#525e62]/70">Client:</span>
                                    <p className="font-medium text-[#525e62]">{appointment.clientName}</p>
                                </div>
                                <div>
                                    <span className="text-[#525e62]/70">Date:</span>
                                    <p className="font-medium text-[#525e62]">
                                        <span className="text-[#525e62]">{new Date(appointment.dateTime).toLocaleDateString('en-US')}</span>
                                    </p>
                                </div>
                                <div>
                                    <span className="text-[#525e62]/70">Heure:</span>
                                    <p className="font-medium text-[#525e62]">
                                        {new Date(appointment.dateTime).toLocaleTimeString('en-US', {
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            hour12: false,
                                        })}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Transaction réalisée */}

                    <>
                        {/* Articles */}
                        <div className="space-y-4">
                            {formData.items.map((item, itemIndex) => (
                                <Card key={itemIndex}>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <div className="space-y-4 rounded-lg border border-[#525e62]/20 p-4">
                                                <div className="flex items-center justify-between">
                                                    <h4 className="font-medium text-[#525e62]">Article {itemIndex + 1}</h4>
                                                </div>

                                                <div className="flex items-center space-x-2 mb-4">
                                                    <Switch
                                                        id={`hasTransaction-${itemIndex}`}
                                                        checked={item.hasTransaction}
                                                        onCheckedChange={(checked) => updateItem(itemIndex, 'hasTransaction', checked)}
                                                    />
                                                    <Label htmlFor={`hasTransaction-${itemIndex}`} className="font-medium text-[#525e62]">
                                                        Transaction réalisée pour cet article
                                                    </Label>
                                                </div>

                                                <div className="grid grid-cols-1 gap-4 md:grid-cols-1">
                                                    <div className="space-y-2">
                                                        <Label className="text-[#525e62]">Type</Label>
                                                        <p className='text-black font-medium'>{item.type}</p>
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label className="text-[#525e62]">Description</Label>
                                                        <Input
                                                            value={item.description}
                                                            onChange={(e) => updateItem(itemIndex, 'description', e.target.value)}
                                                            placeholder="Description détaillée"
                                                        />
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label className="text-[#525e62]">Poids (g)</Label>
                                                        <Input
                                                            type="number"
                                                            step="0.1"
                                                            min = "0"
                                                            value={item.weight}
                                                            onChange={(e) => updateItem(itemIndex, 'weight', e.target.value)}
                                                            placeholder="0.0"
                                                        />
                                                    </div>
                                                </div>

                                                <div className="grid grid-cols-1 gap-4 md:grid-cols-1">
                                                    <div className="space-y-2">
                                                        <Label className="text-[#525e62]">Prix de rachat (€)</Label>
                                                        <Input
                                                            type="number"
                                                            value={item.purchasePrice}
                                                            min = "0"
                                                            onChange={(e) => updateItem(itemIndex, 'purchasePrice', e.target.value)}
                                                            onBlur={calculateTotals}
                                                            placeholder="0"
                                                        />
                                                    </div>
                                                </div>

                                                <div className="space-y-2">
                                                    <Label className="text-[#525e62]">Valeur estimée (€)</Label>
                                                    <Input
                                                        type="number"
                                                        value={item.estimatedValue}
                                                        min = "0"
                                                        onChange={(e) => updateItem(itemIndex, 'estimatedValue', e.target.value)}
                                                        onBlur={calculateTotals}
                                                        placeholder="0"
                                                    />
                                                </div>

                                                {/* Show individual benefit for this item */}
                                                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-sm text-[#525e62]/70">Bénéfice pour cet article:</span>
                                                        <span className={`font-bold ${item.benefit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                            {item.benefit?.toFixed(2) || '0.00'} €
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>

                        {/* Calculs automatiques */}
                        <Card className="bg-green-50">
                            <CardHeader>
                                <CardTitle className="flex items-center text-[#525e62]">
                                    <Calculator className="mr-2 h-5 w-5" />
                                    Calculs Automatiques
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div className="rounded-lg bg-white p-4 text-center">
                                        <p className="text-sm text-[#525e62]/70">Prix de rachat total</p>
                                        <p className="text-2xl font-bold text-[#525e62]">{formData.totalPurchasePrice || '0'} €</p>
                                    </div>
                                    <div className="rounded-lg bg-white p-4 text-center">
                                        <p className="text-sm text-[#525e62]/70">Valeur estimée totale</p>
                                        <p className="text-2xl font-bold text-[#525e62]">{formData.totalEstimatedValue || '0'} €</p>
                                    </div>
                                    <div className="rounded-lg bg-white p-4 text-center">
                                        <p className="text-sm text-[#525e62]/70">Bénéfice estimé</p>
                                        <p className={`text-2xl font-bold ${calculatedProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                            {calculatedProfit.toFixed(2)} €
                                        </p>
                                    </div>
                                    <div className="rounded-lg bg-white p-4 text-center">
                                        <p className="text-sm text-[#525e62]/70">Marge (%)</p>
                                        <p className={`text-2xl font-bold ${profitMargin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                            {profitMargin.toFixed(1)}%
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </>

                    {/* Satisfaction client et suivi */}
                    <div className="grid grid-cols-1 gap-6">
                        <div className="space-y-4">
                        </div>

                        <div className="space-y-2">
                            <Label className="text-[#525e62]">Notes et observations</Label>
                            <Textarea
                                value={formData.notes}
                                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                                rows={6}
                                placeholder="Notes sur le rendez-vous, comportement client, remarques particulières..."
                            />
                        </div>
                    </div>

                    {/* Actions */}
                    <div className="flex justify-end space-x-3 border-t border-[#525e62]/10 pt-6">
                        <Button variant="outline" onClick={onClose}>
                            Annuler
                        </Button>
                        <Button onClick={handleSubmit} className="bg-[#525e62] text-white hover:bg-[#525e62]/90">
                            <Save className="mr-2 h-4 w-4" />
                            Enregistrer
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
