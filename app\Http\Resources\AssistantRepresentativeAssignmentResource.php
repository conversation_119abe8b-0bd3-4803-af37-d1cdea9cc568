<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssistantRepresentativeAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'assistant_id' => $this->assistant_id,
            'representative_id' => $this->representative_id,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Relationships
            'assistant' => new UserResource($this->whenLoaded('assistant')),
            'representative' => new UserResource($this->whenLoaded('representative')),
        ];
    }
}
