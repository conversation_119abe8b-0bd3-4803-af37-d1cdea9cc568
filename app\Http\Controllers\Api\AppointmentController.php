<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AppointmentCollection;
use App\Http\Resources\AppointmentResource;
use App\Http\Traits\ApiResponse;
use App\Models\Appointment;
use App\Models\Purchase;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AppointmentController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of appointments
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $assistantId = $request->get('assistant_id');
        $representativeId = $request->get('representative_id');
        $source = $request->get('source');
        $appointmentType = $request->get('appointment_type');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $includePast = $request->get('include_past', false); // New parameter to include past appointments

        // Always load purchases to check appointment status
        $query = Appointment::with(['assistant', 'representative', 'purchases', 'bonuses']);

        // By default, only show upcoming appointments (unless specifically requested)
        if (!$includePast && !$dateFrom && !$dateTo) {
            $query->where('dateTime', '>=', Carbon::now());
        }

        // Filter by assistant
        if ($assistantId) {
            $query->where('assistant_id', $assistantId);
        }

        // Filter by representative
        if ($representativeId) {
            $query->where('representative_id', $representativeId);
        }

        // Filter by source
        if ($source) {
            $query->where('source', $source);
        }

        // Filter by appointment type
        if ($appointmentType) {
            $query->where('appointment_type', $appointmentType);
        }

        // Filter by date range (overrides upcoming filter)
        if ($dateFrom) {
            $query->whereDate('dateTime', '>=', $dateFrom);
        }
        if ($dateTo) {
            $query->whereDate('dateTime', '<=', $dateTo);
        }

        // Order by date (upcoming appointments first)
        $query->orderBy('dateTime', 'asc');

        $appointments = $query->paginate($perPage);

        return $this->paginatedResponse(
            new AppointmentCollection($appointments),
            'Appointments retrieved successfully'
        );
    }

    /**
     * Store a newly created appointment
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'assistant_id' => 'required|exists:users,id',
            'representative_id' => 'required|exists:users,id',
            'client_name' => 'required|string|max:255',
            'client_phone' => 'required|string|max:20',
            'client_address' => 'required|string|max:255',
            'source' => 'required|in:outbound,leboncoin',
            'date_time' => 'required|date|after:now',
            'notes' => 'nullable|string',
            'items_collection' => 'required|array',
            'appointment_type' => 'required|in:announced,not_announced',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        // Verify that assistant and representative exist and have correct roles
        $assistant = User::find($request->assistant_id);
        $representative = User::find($request->representative_id);

        if ($assistant->role !== 'assistant') {
            return $this->errorResponse('Selected user is not an assistant', 400);
        }

        if ($representative->role !== 'representative') {
            return $this->errorResponse('Selected user is not a representative', 400);
        }

        $appointment = Appointment::create([
            'assistant_id' => $request->assistant_id,
            'representative_id' => $request->representative_id,
            'clientName' => $request->client_name,
            'clientPhone' => $request->client_phone,
            'clientAddress' => $request->client_address,
            'source' => $request->source,
            'dateTime' => $request->date_time,
            'notes' => $request->notes,
            'itemsCollection' => $request->items_collection,
            'appointment_type' => $request->appointment_type,
        ]);

        $appointment->load(['assistant', 'representative']);

        return $this->createdResponse(
            new AppointmentResource($appointment),
            'Appointment created successfully'
        );
    }

    /**
     * Display the specified appointment
     */
    public function show(Appointment $appointment): JsonResponse
    {
        $appointment->load(['assistant', 'representative', 'purchases', 'bonuses']);

        return $this->successResponse(
            new AppointmentResource($appointment),
            'Appointment retrieved successfully'
        );
    }

    /**
     * Update the specified appointment
     */
    public function update(Request $request, Appointment $appointment): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'assistant_id' => 'sometimes|required|exists:users,id',
            'representative_id' => 'sometimes|required|exists:users,id',
            'client_name' => 'sometimes|required|string|max:255',
            'client_phone' => 'sometimes|required|string|max:20',
            'client_address' => 'sometimes|required|string|max:255',
            'source' => 'sometimes|required|in:outbound,leboncoin',
            'date_time' => 'sometimes|required|date',
            'notes' => 'nullable|string',
            'items_collection' => 'sometimes|required|array',
            'appointment_type' => 'sometimes|required|in:announced,not_announced',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $updateData = [];

        if ($request->has('assistant_id')) {
            $assistant = User::find($request->assistant_id);
            if ($assistant->role !== 'assistant') {
                return $this->errorResponse('Selected user is not an assistant', 400);
            }
            $updateData['assistant_id'] = $request->assistant_id;
        }

        if ($request->has('representative_id')) {
            $representative = User::find($request->representative_id);
            if ($representative->role !== 'representative') {
                return $this->errorResponse('Selected user is not a representative', 400);
            }
            $updateData['representative_id'] = $request->representative_id;
        }

        // Map request fields to database fields
        $fieldMapping = [
            'client_name' => 'clientName',
            'client_phone' => 'clientPhone',
            'client_address' => 'clientAddress',
            'source' => 'source',
            'date_time' => 'dateTime',
            'notes' => 'notes',
            'items_collection' => 'itemsCollection',
            'appointment_type' => 'appointment_type',
        ];

        foreach ($fieldMapping as $requestField => $dbField) {
            if ($request->has($requestField)) {
                $updateData[$dbField] = $request->get($requestField);
            }
        }

        $appointment->update($updateData);
        $appointment->load(['assistant', 'representative', 'purchases', 'bonuses']);

        return $this->successResponse(
            new AppointmentResource($appointment),
            'Appointment updated successfully'
        );
    }

    /**
     * Remove the specified appointment
     */
    public function destroy(Appointment $appointment): JsonResponse
    {
        $appointment->delete();

        return $this->successResponse(null, 'Appointment deleted successfully');
    }

    /**
     * Get appointments for current user
     */
    public function myAppointments(Request $request): JsonResponse
    {
        $user = $request->user();
        $perPage = $request->get('per_page', 15);
        $includePast = $request->get('include_past', false); // New parameter to include past appointments

        // Always load purchases to check appointment status
        $query = Appointment::with(['assistant', 'representative', 'purchases', 'bonuses']);

        if ($user->role === 'assistant') {
            $query->where('assistant_id', $user->id);
        } elseif ($user->role === 'representative') {
            $query->where('representative_id', $user->id);
        } else {
            return $this->errorResponse('User role not supported for this endpoint', 400);
        }

        // By default, only show upcoming appointments (unless specifically requested)
        if (!$includePast) {
            $query->where('dateTime', '>=', Carbon::now());
        }

        // Order by date (upcoming appointments first)
        $query->orderBy('dateTime', 'asc');
        $appointments = $query->paginate($perPage);

        return $this->paginatedResponse(
            new AppointmentCollection($appointments),
            'User appointments retrieved successfully'
        );
    }

    /**
     * Create a new RDV (appointment) - Mobile optimized endpoint
     */
    public function createRdv(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'representative_id' => 'required|exists:users,id',
            'client_name' => 'required|string|max:255',
            'client_phone' => 'required|string|max:20',
            'client_address' => 'required|string|max:500',
            'source' => 'required|in:outbound,leboncoin',
            'date_time' => 'required|date|after:now',
            'notes' => 'nullable|string|max:1000',
            'items_collection' => 'required|array|min:1',
            'items_collection.*' => 'required|string',
            'appointment_type' => 'required|in:announced,not_announced',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $user = $request->user();

        // Verify that the current user is an assistant
        if ($user->role !== 'assistant') {
            return $this->forbiddenResponse('Only assistants can create appointments');
        }

        // Verify that the representative exists and has correct role
        $representative = User::find($request->representative_id);
        if ($representative->role !== 'representative') {
            return $this->errorResponse('Selected user is not a representative', 400);
        }

        // Check if representative is activated
        if (!$representative->is_activated) {
            return $this->errorResponse('Selected representative is not activated', 400);
        }

        // Check if assistant is assigned to this representative (optional business rule)
        $isAssigned = $user->assignedRepresentatives()->where('representative_id', $request->representative_id)->exists();
        if (!$isAssigned) {
            return $this->errorResponse('You are not assigned to this representative', 403);
        }

        try {
            $appointment = Appointment::create([
                'assistant_id' => $user->id,
                'representative_id' => $request->representative_id,
                'clientName' => $request->client_name,
                'clientPhone' => $request->client_phone,
                'clientAddress' => $request->client_address,
                'source' => $request->source,
                'dateTime' => $request->date_time,
                'notes' => $request->notes,
                'itemsCollection' => $request->items_collection,
                'appointment_type' => $request->appointment_type,
            ]);

            $appointment->load(['assistant', 'representative']);

            return $this->createdResponse(
                new AppointmentResource($appointment),
                'RDV created successfully'
            );

        } catch (\Exception $e) {
            return $this->serverErrorResponse('Failed to create appointment. Please try again.');
        }
    }

    /**
     * Get representative dashboard stats for today
     * Enhanced with total appointments, conversion rate, profit margin, and completed appointments
     */
    public function representativeDashboardStats(Request $request): JsonResponse
    {
        $user = $request->user();

        // Verify that the current user is a representative
        if ($user->role !== 'representative') {
            return $this->forbiddenResponse('Only representatives can access dashboard stats');
        }

        $today = Carbon::today();

        // TODAY'S STATS
        // Today's appointments
        $todaysAppointments = Appointment::where('representative_id', $user->id)
            ->whereDate('dateTime', $today)
            ->count();

        // Today's realized appointments (appointments with purchases)
        $todaysRealizedAppointments = Appointment::where('representative_id', $user->id)
            ->whereDate('dateTime', $today)
            ->whereHas('purchases', function ($query) {
                $query->where('status', 'purchased');
            })
            ->count();

        // Today's revenue (chiffre d'affaires)
        $todaysRevenue = Purchase::whereHas('appointment', function ($query) use ($user, $today) {
            $query->where('representative_id', $user->id)
                  ->whereDate('dateTime', $today);
        })
        ->where('status', 'purchased')
        ->sum('resale_price') ?? 0;

        // Today's benefit
        $todaysBenefit = Purchase::whereHas('appointment', function ($query) use ($user, $today) {
            $query->where('representative_id', $user->id)
                  ->whereDate('dateTime', $today);
        })
        ->where('status', 'purchased')
        ->sum('benefit') ?? 0;

        // Today's total cost (buy prices)
        $todaysTotalCost = Purchase::whereHas('appointment', function ($query) use ($user, $today) {
            $query->where('representative_id', $user->id)
                  ->whereDate('dateTime', $today);
        })
        ->where('status', 'purchased')
        ->sum('buy_price') ?? 0;

        // Today's conversion rate (realization rate)
        $todaysConversionRate = $todaysAppointments > 0
            ? round(($todaysRealizedAppointments / $todaysAppointments) * 100, 1)
            : 0;

        // Today's profit margin
        $todaysProfitMargin = $todaysRevenue > 0
            ? round(($todaysBenefit / $todaysRevenue) * 100, 1)
            : 0;

        // WEEKLY STATS (Current week for this representative)
        // Get current week start (Monday) and end (Sunday)
        $weekStart = Carbon::now()->startOfWeek(Carbon::MONDAY);
        $weekEnd = Carbon::now()->endOfWeek(Carbon::SUNDAY);

        // Weekly appointments
        $weeklyAppointments = Appointment::where('representative_id', $user->id)
            ->whereBetween('dateTime', [$weekStart, $weekEnd])
            ->count();

        // Weekly completed appointments
        $weeklyCompletedAppointments = Appointment::where('representative_id', $user->id)
            ->whereBetween('dateTime', [$weekStart, $weekEnd])
            ->whereHas('purchases', function ($query) {
                $query->where('status', 'purchased');
            })
            ->count();

        // Weekly conversion rate
        $weeklyConversionRate = $weeklyAppointments > 0
            ? round(($weeklyCompletedAppointments / $weeklyAppointments) * 100, 1)
            : 0;

        // Weekly revenue
        $weeklyRevenue = Purchase::whereHas('appointment', function ($query) use ($user, $weekStart, $weekEnd) {
            $query->where('representative_id', $user->id)
                  ->whereBetween('dateTime', [$weekStart, $weekEnd]);
        })
        ->where('status', 'purchased')
        ->sum('resale_price') ?? 0;

        // Weekly benefit
        $weeklyBenefit = Purchase::whereHas('appointment', function ($query) use ($user, $weekStart, $weekEnd) {
            $query->where('representative_id', $user->id)
                  ->whereBetween('dateTime', [$weekStart, $weekEnd]);
        })
        ->where('status', 'purchased')
        ->sum('benefit') ?? 0;

        // Weekly total cost
        $weeklyTotalCost = Purchase::whereHas('appointment', function ($query) use ($user, $weekStart, $weekEnd) {
            $query->where('representative_id', $user->id)
                  ->whereBetween('dateTime', [$weekStart, $weekEnd]);
        })
        ->where('status', 'purchased')
        ->sum('buy_price') ?? 0;

        // Weekly profit margin
        $weeklyProfitMargin = $weeklyRevenue > 0
            ? round(($weeklyBenefit / $weeklyRevenue) * 100, 1)
            : 0;

        $stats = [
            // Today's performance
            'today' => [
                'appointments' => $todaysAppointments,
                'completed_appointments' => $todaysRealizedAppointments,
                'conversion_rate' => $todaysConversionRate,
                'revenue' => (float) $todaysRevenue,
                'total_cost' => (float) $todaysTotalCost,
                'benefit' => (float) $todaysBenefit,
                'profit_margin' => $todaysProfitMargin,
                'date' => $today->toDateString(),
            ],

            // Weekly performance
            'weekly' => [
                'total_appointments' => $weeklyAppointments,
                'completed_appointments' => $weeklyCompletedAppointments,
                'conversion_rate' => $weeklyConversionRate,
                'total_revenue' => (float) $weeklyRevenue,
                'total_cost' => (float) $weeklyTotalCost,
                'total_benefit' => (float) $weeklyBenefit,
                'profit_margin' => $weeklyProfitMargin,
                'week_start' => $weekStart->toDateString(),
                'week_end' => $weekEnd->toDateString(),
                'week_number' => $weekStart->weekOfYear,
                'year' => $weekStart->year,
            ],

            // Legacy fields for backward compatibility
            'todays_appointments' => $todaysAppointments,
            'todays_realized_appointments' => $todaysRealizedAppointments,
            'todays_revenue' => (float) $todaysRevenue,
            'todays_benefit' => (float) $todaysBenefit,
            'realization_rate' => $todaysConversionRate,
            'date' => $today->toDateString(),
        ];

        return $this->successResponse(
            $stats,
            'Representative dashboard stats retrieved successfully'
        );
    }
}
