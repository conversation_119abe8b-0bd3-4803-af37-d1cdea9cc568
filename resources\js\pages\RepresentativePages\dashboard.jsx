import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { Calendar, Clock, DollarSign, Package, TrendingUp } from 'lucide-react';

export default function DashboardPage() {
    const { auth, todayAppointments, recentPurchases, stats } = usePage().props;

    const breadcrumbs = [
        {
            title: 'Tableau de bord',
            href: '/representative/dashboard',
        },
    ];

    const getStatusColor = (status) => {
        switch (status) {
            case 'leboncoin':
                return 'bg-[#FF670A] text-gray-100 border-orange-500';
            case 'outbound':
                return 'bg-blue-300 text-blue-800 border-blue-200';
            default:
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        }
    };

    const getTypeColor = (type) => {
        switch (type) {
            case 'Or':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'Montre':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'Brocante':
                return 'bg-green-100 text-green-800 border-green-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="space-y-6 p-3 lg:p-6">
                <div>
                    <h1 className="text-3xl font-bold text-[#525e62]">Bienvenue, {auth?.user.name}</h1>
                    <p className="text-[#525e62]/70">Voici un aperçu de votre activité en tant que Représentant Commercial.</p>
                </div>
                {/* Stats Cards */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    {/* Today's Appointments */}
                    <Card className="border-0 bg-white shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-[#525e62]/70">RDV Aujourd'hui</CardTitle>
                            <Calendar className="h-4 w-4 text-[#525e62]/70" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-[#525e62]">{stats.today.appointments}</div>
                        </CardContent>
                    </Card>

                    {/* Today's Revenue */}
                    <Card className="border-0 bg-white shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-[#525e62]/70">Chiffre d'affaires (Aujourd'hui)</CardTitle>
                            <DollarSign className="h-4 w-4 text-[#525e62]/70" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-[#525e62]">{stats.today.revenue.toLocaleString()} €</div>
                        </CardContent>
                    </Card>

                    {/* Weekly Performance */}
                    <Card className="border-0 bg-white shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-[#525e62]/70">Performance Hebdo</CardTitle>
                            <TrendingUp className="h-4 w-4 text-[#525e62]/70" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-[#525e62]">
                                {stats.week.purchases} <span className="text-sm font-normal text-[#525e62]/70">rachats</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    {/* Today's Appointments List */}
                    <Card className="border-0 bg-white shadow-lg">
                        <CardHeader>
                            <CardTitle className="flex items-center text-[#525e62]">
                                <Clock className="mr-2 h-5 w-5" />
                                Vos RDV Aujourd'hui ({todayAppointments.length})
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {todayAppointments.length > 0 ? (
                                    todayAppointments.map((appointment) => (
                                        <div
                                            key={appointment.id}
                                            className="flex items-center justify-between rounded-lg border border-[#525e62]/10 p-4"
                                        >
                                            <div className="flex items-center space-x-4">
                                                <div className="text-center">
                                                    <p className="text-sm font-medium text-[#525e62]">
                                                        {new Date(appointment.dateTime).toLocaleTimeString('fr-FR', {
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                        })}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="font-medium text-[#525e62]">{appointment.clientName}</p>
                                                    <Badge className={getStatusColor(appointment.source)}>
                                                        {appointment.source === 'outbound' ? 'sortant' : 'Leboncoin'}
                                                    </Badge>
                                                </div>
                                            </div>
                                            {/* <Badge className={getTypeColor(appointment.appointment_type)}>
                                                {appointment.appointment_type === 'announced' ? 'annoncé' : 'non annoncé'}
                                            </Badge> */}
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-center text-[#525e62]/70">Aucun rendez-vous prévu aujourd'hui</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Purchases */}
                    <Card className="border-0 bg-white shadow-lg">
                        <CardHeader>
                            <CardTitle className="flex items-center text-[#525e62]">
                                <Package className="mr-2 h-5 w-5" />
                                Derniers Rachats ({recentPurchases.length})
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentPurchases.length > 0 ? (
                                    recentPurchases.map((purchase) => (
                                        <div
                                            key={purchase.id}
                                            className="flex flex-wrap items-center justify-between rounded-lg border border-[#525e62]/10 p-4"
                                        >
                                            <div>
                                                <p className="font-medium text-[#525e62]">{purchase.clientName}</p>
                                                <p className="text-sm text-[#525e62]/70">{purchase.date}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-bold text-[#525e62]">€{purchase.amount.toLocaleString()} (CA)</p>
                                                {/* <Badge className={`${getTypeColor(purchase.type)} flex-wrap w-[70%]`}>{purchase.type}</Badge> */}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-center text-[#525e62]/70">Aucun rachat récent</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Monthly Overview */}
                <Card className="border-0 bg-white shadow-lg">
                    <CardHeader>
                        <CardTitle className="flex items-center text-[#525e62]">
                            <TrendingUp className="mr-2 h-5 w-5" />
                            Aperçu Mensuel
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div className="rounded-lg bg-blue-50 p-4">
                                <p className="text-sm text-[#525e62]/70">RDV ce mois</p>
                                <p className="text-2xl font-bold text-[#525e62]">{stats.month.appointments}</p>
                            </div>
                            <div className="rounded-lg bg-green-50 p-4">
                                <p className="text-sm text-[#525e62]/70">Rachats ce mois</p>
                                <p className="text-2xl font-bold text-[#525e62]">{stats.month.purchases}</p>
                            </div>
                            <div className="rounded-lg bg-purple-50 p-4">
                                <p className="text-sm text-[#525e62]/70">CA Mensuel</p>
                                <p className="text-2xl font-bold text-[#525e62]">{stats.month.revenue.toLocaleString()} €</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
