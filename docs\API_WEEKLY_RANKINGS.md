# Weekly Team Rankings API

This endpoint provides weekly team rankings for representatives based on their performance metrics.

## Endpoint Details

**URL:** `GET /api/v1/representatives/weekly-rankings`  
**Authentication:** Required (<PERSON><PERSON>)  
**Role:** Any authenticated user can access this endpoint  

## Request Format

### Headers
```
Authorization: Bearer {your_token}
Content-Type: application/json
```

### Parameters
None required. The endpoint automatically calculates rankings for the current week (Monday to Sunday).

## Response Format

### Success Response (200)
```json
{
    "success": true,
    "message": "Weekly team rankings retrieved successfully",
    "data": {
        "rankings": [
            {
                "rank": 1,
                "representative": {
                    "id": 123,
                    "name": "<PERSON>",
                    "email": "<EMAIL>",
                    "initials": "JD",
                    "joined_date": "2024-01-15"
                },
                "performance": {
                    "total_appointments": 15,
                    "completed_appointments": 12,
                    "completion_rate": 80.0,
                    "weekly_revenue": 2500.50
                },
                "badges": {
                    "top_performer": true,
                    "rank_badge": "🥇"
                }
            },
            {
                "rank": 2,
                "representative": {
                    "id": 124,
                    "name": "<PERSON>",
                    "email": "<EMAIL>",
                    "initials": "J<PERSON>",
                    "joined_date": "2024-02-01"
                },
                "performance": {
                    "total_appointments": 18,
                    "completed_appointments": 11,
                    "completion_rate": 61.1,
                    "weekly_revenue": 2200.00
                },
                "badges": {
                    "top_performer": true,
                    "rank_badge": "🥈"
                }
            }
        ],
        "meta": {
            "week_period": {
                "start": "2024-08-19",
                "end": "2024-08-25",
                "week_number": 34,
                "year": 2024
            },
            "total_representatives": 2,
            "generated_at": "2024-08-22T10:30:00.000000Z"
        }
    }
}
```

### Error Responses

#### Unauthorized (401)
```json
{
    "success": false,
    "message": "Unauthorized"
}
```

#### Server Error (500)
```json
{
    "success": false,
    "message": "Failed to retrieve weekly rankings"
}
```

## Data Fields Explanation

### Representative Object
- **id**: Unique identifier for the representative
- **name**: Full name of the representative
- **email**: Email address of the representative
- **initials**: Auto-generated initials from the name (max 2 characters)
- **joined_date**: Date when the representative joined (YYYY-MM-DD format)

### Performance Object
- **total_appointments**: Total number of appointments scheduled this week
- **completed_appointments**: Number of appointments with purchases (status = 'purchased')
- **completion_rate**: Percentage of completed appointments (completed/total * 100)
- **weekly_revenue**: Total revenue from completed appointments (sum of resale_price)

### Badges Object
- **top_performer**: Boolean indicating if representative is in top 3
- **rank_badge**: Emoji badge for top 3 positions (🥇🥈🥉)

## Business Logic

1. **Week Calculation**: Current week from Monday to Sunday
2. **Ranking Logic**: 
   - Primary: Number of completed appointments (DESC)
   - Secondary: Weekly revenue (DESC)
3. **Completion Rate**: (completed_appointments / total_appointments) * 100
4. **Revenue Calculation**: Sum of `resale_price` from purchases with status 'purchased'
5. **Representative Filter**: Only active representatives (is_activated = true)
6. **Limit**: Top 10 representatives only

## Mobile App Integration Example

```javascript
// React Native example
const fetchWeeklyRankings = async () => {
    try {
        const response = await fetch('/api/v1/representatives/weekly-rankings', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            setRankings(data.data.rankings);
            setWeekInfo(data.data.meta.week_period);
        }
    } catch (error) {
        console.error('Error fetching rankings:', error);
    }
};
```

## Performance Considerations

- Query is optimized with proper joins and indexing
- Limited to top 10 results to reduce payload size
- Uses database aggregation for better performance
- Includes error handling and logging

## Testing

You can test this endpoint using curl:

```bash
curl -X GET "https://your-domain.com/api/v1/representatives/weekly-rankings" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```
