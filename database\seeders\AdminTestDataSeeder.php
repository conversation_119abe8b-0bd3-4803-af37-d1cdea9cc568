<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Purchase;
use App\Models\Bonus;
use App\Models\AssistantRepresentativeAssignment;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Console\Command;

class AdminTestDataSeeder extends Seeder
{
    protected $command = null;

    public function setCommand(Command $command)
    {
        $this->command = $command;
    }

    private function info($message)
    {
        if ($this->command) {
            $this->command->info($message);
        }
    }

    private function line($message)
    {
        if ($this->command) {
            $this->command->line($message);
        }
    }

    public function run()
    {
        $this->info('🚀 Starting comprehensive test data generation...');
        $this->line('📅 Time Range: ' . Carbon::now()->subMonth()->format('M Y') . ' - ' . Carbon::now()->format('M Y') . ' (granular date distribution)');
        $this->line('👥 Users: All 16 users (9 SA, 4 SR, 1 Admin, 1 HR, 1 EA)');
        $this->line('🕒 Business Hours: Weekdays 9AM-6PM (peak 10-11AM, 2-3PM), Weekends 10AM-4PM');
        $this->line('');

        // Step 1: Seed users first
        $this->info('👥 Step 1: Creating users...');
        $this->call(UserSeeder::class);
        
        // Step 2: Create assignments
        $this->info('🔗 Step 2: Creating SA-SR assignments...');
        $this->createAssignments();
        
        // Step 3: Generate appointments over 2 months
        $this->info('📅 Step 3: Generating appointments (last 2 months)...');
        $appointments = $this->generateAppointments();
        
        // Step 4: Generate purchases
        $this->info('💰 Step 4: Generating purchases...');
        $purchases = $this->generatePurchases($appointments);
        
        // Step 5: Generate bonuses
        $this->info('🎁 Step 5: Generating bonuses...');
        $this->generateBonuses($appointments, $purchases);
        
        $this->info('');
        $this->info('✅ Test data generation completed successfully!');
        $this->line('📊 Generated data summary:');
        $this->line('   • Users: 16 total');
        $this->line('   • Appointments: ' . $appointments->count());
        $this->line('   • Purchases: ' . $purchases->count());
        $this->line('   • Time span: 2 months');
    }

    private function createAssignments()
    {
        $assistants = User::where('role', 'assistant')->get();
        $representatives = User::where('role', 'representative')->get();
        
        // Create balanced assignments - each assistant gets 1-2 representatives
        foreach ($assistants as $index => $assistant) {
            // Assign representatives in a round-robin fashion
            $rep1 = $representatives[$index % $representatives->count()];
            $rep2 = $representatives[($index + 1) % $representatives->count()];
            
            AssistantRepresentativeAssignment::create([
                'assistant_id' => $assistant->id,
                'representative_id' => $rep1->id,
            ]);
            
            // Some assistants get a second representative
            if ($index % 2 === 0) {
                AssistantRepresentativeAssignment::create([
                    'assistant_id' => $assistant->id,
                    'representative_id' => $rep2->id,
                ]);
            }
        }
        
        $this->line('   ✓ Created SA-SR assignments');
    }

    private function generateAppointments()
    {
        $assistants = User::where('role', 'assistant')->get();
        $representatives = User::where('role', 'representative')->get();
        $appointments = collect();

        $sources = ['outbound', 'leboncoin'];
        $appointmentTypes = ['announced', 'not_announced'];
        $itemTypes = [
            'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
            'montre et montre a gousset (en argent et en or)',
            'piece de monnaie en or et en argent',
            'lingot et piece d\'investissement en or et en argent',
            'objet en argent massif (couverts, plateaux, etc.)',
            'dentaire en or'
        ];

        // Generate appointments for both previous month and current month
        $previousMonth = Carbon::now()->subMonth()->startOfMonth();
        $currentMonth = Carbon::now()->startOfMonth();
        $today = Carbon::now();

        foreach ($assistants as $assistant) {
            $assignedReps = $assistant->assignedRepresentatives;
            if ($assignedReps->isEmpty()) {
                $assignedReps = $representatives; // Fallback
            }

            // Generate appointments for previous month (10-15 appointments)
            $prevMonthCount = rand(10, 15);
            $prevMonthDates = $this->generateBusinessDates($previousMonth, $previousMonth->copy()->endOfMonth(), $prevMonthCount);

            foreach ($prevMonthDates as $date) {
                $representative = $assignedReps->random();
                $appointmentTime = $this->setBusinessHours($date);

                $appointment = Appointment::create([
                    'assistant_id' => $assistant->id,
                    'representative_id' => $representative->id,
                    'clientName' => $this->generateClientName(),
                    'clientPhone' => $this->generatePhoneNumber(),
                    'clientAddress' => $this->generateAddress(),
                    'source' => $sources[array_rand($sources)],
                    'dateTime' => $appointmentTime,
                    'notes' => $this->generateNotes(),
                    'itemsCollection' => [$itemTypes[array_rand($itemTypes)]],
                    'appointment_type' => $appointmentTypes[array_rand($appointmentTypes)],
                    'created_at' => $appointmentTime->copy()->subHours(rand(1, 48)),
                    'updated_at' => $appointmentTime->copy()->addHours(rand(1, 24)),
                ]);

                $appointments->push($appointment);
            }

            // Generate appointments for current month (8-12 appointments up to today)
            $currentMonthCount = rand(8, 12);
            $currentMonthDates = $this->generateBusinessDates($currentMonth, $today, $currentMonthCount);

            foreach ($currentMonthDates as $date) {
                $representative = $assignedReps->random();
                $appointmentTime = $this->setBusinessHours($date);

                $appointment = Appointment::create([
                    'assistant_id' => $assistant->id,
                    'representative_id' => $representative->id,
                    'clientName' => $this->generateClientName(),
                    'clientPhone' => $this->generatePhoneNumber(),
                    'clientAddress' => $this->generateAddress(),
                    'source' => $sources[array_rand($sources)],
                    'dateTime' => $appointmentTime,
                    'notes' => $this->generateNotes(),
                    'itemsCollection' => [$itemTypes[array_rand($itemTypes)]],
                    'appointment_type' => $appointmentTypes[array_rand($appointmentTypes)],
                    'created_at' => $appointmentTime->copy()->subHours(rand(1, 48)),
                    'updated_at' => $appointmentTime->copy()->addHours(rand(1, 24)),
                ]);

                $appointments->push($appointment);
            }
        }

        $this->line('   ✓ Generated ' . $appointments->count() . ' appointments');
        $this->line('   ✓ Previous month: ' . $appointments->filter(function($apt) use ($previousMonth) {
            return $apt->dateTime->month === $previousMonth->month;
        })->count() . ' appointments');
        $this->line('   ✓ Current month: ' . $appointments->filter(function($apt) use ($currentMonth) {
            return $apt->dateTime->month === $currentMonth->month;
        })->count() . ' appointments');

        return $appointments;
    }

    private function generatePurchases($appointments)
    {
        $purchases = collect();

        foreach ($appointments as $appointment) {
            // 60-70% of appointments should have purchases
            $hasPurchases = rand(1, 100) <= 65;

            if ($hasPurchases) {
                // Generate 1-3 purchase items per appointment
                $itemCount = rand(1, 3);

                for ($i = 0; $i < $itemCount; $i++) {
                    $status = rand(1, 100) <= 75 ? 'purchased' : 'not_purchased'; // 75% success rate

                    if ($status === 'purchased') {
                        $buyPrice = rand(50, 800);
                        $resalePrice = $buyPrice + rand(20, 300);
                        $weight = rand(10, 1000) / 10; // 1.0 to 100.0 grams
                        $benefit = $resalePrice - $buyPrice;
                    } else {
                        $buyPrice = 0;
                        $resalePrice = 0;
                        $weight = rand(10, 1000) / 10;
                        $benefit = 0;
                    }

                    $purchase = Purchase::create([
                        'appointment_id' => $appointment->id,
                        'item_type' => $appointment->itemsCollection[0],
                        'description' => $this->generateItemDescription($appointment->itemsCollection[0]),
                        'weight' => $weight,
                        'buy_price' => $buyPrice,
                        'benefit' => $benefit,
                        'resale_price' => $resalePrice,
                        'status' => $status,
                        'notes' => $status === 'purchased' ? 'Transaction réussie' : 'Client a décliné l\'offre',
                        'created_at' => $appointment->created_at->copy()->addHours(rand(1, 24)),
                        'updated_at' => $appointment->created_at->copy()->addHours(rand(1, 24)),
                    ]);

                    $purchases->push($purchase);
                }
            }
        }

        $this->line('   ✓ Generated ' . $purchases->count() . ' purchases');
        return $purchases;
    }

    private function generateBonuses($appointments, $purchases)
    {
        $bonuses = collect();

        // Group appointments by assistant and date for bonus calculations
        $appointmentsByAssistantAndDate = $appointments->groupBy(function ($appointment) {
            return $appointment->assistant_id . '_' . $appointment->created_at->format('Y-m-d');
        });

        foreach ($appointments as $appointment) {
            // 1. STANDARD bonus: outbound + announced = 5€
            if ($appointment->appointment_type === 'announced' && $appointment->source === 'outbound') {
                $bonus = Bonus::create([
                    'user_id' => $appointment->assistant_id,
                    'appointment_id' => $appointment->id,
                    'amount' => 5,
                    'type' => 'STANDARD',
                    'reason' => 'sortant + annoncé',
                    'created_at' => $appointment->created_at->copy()->addHours(rand(1, 4)),
                    'updated_at' => $appointment->created_at->copy()->addHours(rand(1, 4)),
                ]);
                $bonuses->push($bonus);
            }
        }

        // 2. DAILY_SUPP bonus: LeBonCoin milestones
        foreach ($appointmentsByAssistantAndDate as $key => $dayAppointments) {
            [$assistantId] = explode('_', $key);

            $leboncoinCount = $dayAppointments->where('source', 'leboncoin')->count();

            if ($leboncoinCount >= 6) {
                $amount = 10; // 6+ = 10€
                if ($leboncoinCount >= 7) $amount = 15; // 7+ = 15€
                if ($leboncoinCount >= 8) $amount = 20; // 8+ = 20€

                $bonus = Bonus::create([
                    'user_id' => $assistantId,
                    'appointment_id' => $dayAppointments->first()->id,
                    'amount' => $amount,
                    'type' => 'DAILY_SUPP',
                    'reason' => "LeBonCoin milestone: {$leboncoinCount} RDV",
                    'created_at' => $dayAppointments->first()->created_at->copy()->endOfDay(),
                    'updated_at' => $dayAppointments->first()->created_at->copy()->endOfDay(),
                ]);
                $bonuses->push($bonus);
            }
        }

        // 3. Additional STANDARD bonuses (performance bonuses distributed across dates)
        $assistants = User::where('role', 'assistant')->get();
        $previousMonth = Carbon::now()->subMonth();
        $currentMonth = Carbon::now();

        foreach ($assistants as $assistant) {
            // 30% chance of getting a performance bonus in previous month
            if (rand(1, 100) <= 30) {
                $bonusDate = $this->generateRandomBusinessDate($previousMonth->startOfMonth(), $previousMonth->endOfMonth());
                $bonus = Bonus::create([
                    'user_id' => $assistant->id,
                    'appointment_id' => null,
                    'amount' => rand(10, 50),
                    'type' => 'STANDARD',
                    'reason' => 'Performance exceptionnelle',
                    'created_at' => $bonusDate,
                    'updated_at' => $bonusDate->copy()->addHours(rand(1, 4)),
                ]);
                $bonuses->push($bonus);
            }

            // 25% chance of getting a performance bonus in current month
            if (rand(1, 100) <= 25) {
                $bonusDate = $this->generateRandomBusinessDate($currentMonth->startOfMonth(), Carbon::now());
                $bonus = Bonus::create([
                    'user_id' => $assistant->id,
                    'appointment_id' => null,
                    'amount' => rand(10, 50),
                    'type' => 'STANDARD',
                    'reason' => 'Performance exceptionnelle',
                    'created_at' => $bonusDate,
                    'updated_at' => $bonusDate->copy()->addHours(rand(1, 4)),
                ]);
                $bonuses->push($bonus);
            }
        }

        $this->line('   ✓ Generated ' . $bonuses->count() . ' bonuses');
        return $bonuses;
    }

    private function generateClientName()
    {
        $firstNames = ['Ahmed', 'Fatima', 'Mohammed', 'Aicha', 'Youssef', 'Khadija', 'Omar', 'Zineb', 'Karim', 'Leila', 'Hassan', 'Nadia', 'Rachid', 'Amina', 'Said', 'Malika'];
        $lastNames = ['Alami', 'Benali', 'Cherkaoui', 'Douiri', 'El Fassi', 'Ghazi', 'Hajji', 'Idrissi', 'Jamal', 'Kettani', 'Lahlou', 'Mansouri', 'Naciri', 'Ouali', 'Qadiri', 'Rami'];

        return $firstNames[array_rand($firstNames)] . ' ' . $lastNames[array_rand($lastNames)];
    }

    private function generatePhoneNumber()
    {
        $prefixes = ['06', '07'];
        return $prefixes[array_rand($prefixes)] . rand(10000000, 99999999);
    }

    private function generateAddress()
    {
        $cities = ['Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Tanger', 'Agadir', 'Meknès', 'Oujda', 'Kenitra', 'Tétouan'];
        $streets = ['Rue Mohammed V', 'Avenue Hassan II', 'Boulevard Zerktouni', 'Rue de la Liberté', 'Avenue des FAR', 'Rue Allal Ben Abdellah'];

        return rand(1, 999) . ' ' . $streets[array_rand($streets)] . ', ' . $cities[array_rand($cities)];
    }

    private function generateNotes()
    {
        $notes = [
            'Client très intéressé',
            'Rendez-vous confirmé par téléphone',
            'Client a plusieurs objets à vendre',
            'Première visite, client curieux',
            'Client recommandé par un ami',
            null, // Some appointments have no notes
            null,
            'Client pressé de vendre',
            'Objets hérités de famille',
        ];

        return $notes[array_rand($notes)];
    }

    private function generateItemDescription($itemType)
    {
        $descriptions = [
            'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)' => [
                'Bague en or 18k avec pierre',
                'Collier en argent massif',
                'Bracelet en or jaune',
                'Boucles d\'oreilles dépareillées',
                'Chaîne en or blanc cassée',
            ],
            'montre et montre a gousset (en argent et en or)' => [
                'Montre Omega vintage',
                'Montre à gousset en or',
                'Montre Rolex ancienne',
                'Montre de poche en argent',
                'Montre automatique suisse',
            ],
            'piece de monnaie en or et en argent' => [
                'Pièce Napoléon 20 francs or',
                'Pièce 50 francs Hercule argent',
                'Pièce souverain anglais',
                'Pièce 10 francs or Marianne',
                'Pièce 5 francs argent Semeuse',
            ],
        ];

        $typeDescriptions = $descriptions[$itemType] ?? ['Objet en métal précieux'];
        return $typeDescriptions[array_rand($typeDescriptions)];
    }

    /**
     * Generate specific business dates within a date range, favoring weekdays
     */
    private function generateBusinessDates($startDate, $endDate, $count)
    {
        $dates = collect();
        $current = $startDate->copy();
        $availableDates = collect();

        // Collect all available dates, giving preference to weekdays
        while ($current->lte($endDate)) {
            if ($current->isWeekday()) {
                // Add weekdays multiple times to increase probability
                $availableDates->push($current->copy());
                $availableDates->push($current->copy());
                $availableDates->push($current->copy());
            } else {
                // Add weekends once (lower probability)
                $availableDates->push($current->copy());
            }
            $current->addDay();
        }

        // Randomly select dates from available dates
        for ($i = 0; $i < $count && $availableDates->isNotEmpty(); $i++) {
            $randomDate = $availableDates->random();
            $dates->push($randomDate);

            // Remove some instances to avoid too much clustering on same day
            $availableDates = $availableDates->reject(function($date) use ($randomDate) {
                return $date->isSameDay($randomDate) && rand(1, 100) <= 70;
            });
        }

        return $dates->sortBy(function($date) {
            return $date->timestamp;
        });
    }

    /**
     * Set realistic business hours for appointments
     */
    private function setBusinessHours($date)
    {
        $businessDate = $date->copy();

        if ($businessDate->isWeekday()) {
            // Weekdays: 9 AM to 6 PM with higher probability during peak hours
            $hour = $this->getWeightedBusinessHour();
        } else {
            // Weekends: 10 AM to 4 PM (limited hours)
            $hour = rand(10, 16);
        }

        $minute = rand(0, 59);
        return $businessDate->setTime($hour, $minute);
    }

    /**
     * Get weighted business hours (more appointments during peak times)
     */
    private function getWeightedBusinessHour()
    {
        $weights = [
            9 => 5,   // 9 AM - low
            10 => 15, // 10 AM - high
            11 => 20, // 11 AM - peak
            12 => 10, // 12 PM - lunch (lower)
            13 => 8,  // 1 PM - lunch (lower)
            14 => 20, // 2 PM - peak
            15 => 18, // 3 PM - high
            16 => 15, // 4 PM - high
            17 => 10, // 5 PM - medium
            18 => 5   // 6 PM - low
        ];

        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        $currentWeight = 0;

        foreach ($weights as $hour => $weight) {
            $currentWeight += $weight;
            if ($random <= $currentWeight) {
                return $hour;
            }
        }

        return 14; // Default to 2 PM
    }

    /**
     * Generate a single random business date within a range
     */
    private function generateRandomBusinessDate($startDate, $endDate)
    {
        $dates = $this->generateBusinessDates($startDate, $endDate, 1);
        return $dates->first() ?? $startDate;
    }
}
