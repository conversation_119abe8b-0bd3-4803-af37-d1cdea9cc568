<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'appointment_id' => $this->appointment_id,
            'status' => $this->status,
            'description' => $this->description,
            'item_type' => $this->item_type,
            'weight' => $this->weight ? (float) $this->weight : null,
            'buy_price' => $this->buy_price ? (float) $this->buy_price : null,
            'resale_price' => $this->resale_price ? (float) $this->resale_price : null,
            'benefit' => $this->benefit ? (float) $this->benefit : null,
            'notes' => $this->notes,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Relationships
            'appointment' => new AppointmentResource($this->whenLoaded('appointment')),
            
            // Computed fields for mobile app convenience
            'profit_margin' => $this->when(
                $this->buy_price && $this->resale_price && $this->resale_price > 0,
                function () {
                    return round((($this->resale_price - $this->buy_price) / $this->resale_price) * 100, 2);
                }
            ),
            'is_profitable' => $this->when(
                $this->buy_price && $this->resale_price,
                function () {
                    return $this->resale_price > $this->buy_price;
                }
            ),
            'weight_display' => $this->when(
                $this->weight,
                function () {
                    return $this->weight . 'g';
                }
            ),
            'buy_price_formatted' => $this->when(
                $this->buy_price,
                function () {
                    return '€' . number_format($this->buy_price, 2);
                }
            ),
            'resale_price_formatted' => $this->when(
                $this->resale_price,
                function () {
                    return '€' . number_format($this->resale_price, 2);
                }
            ),
            'benefit_formatted' => $this->when(
                $this->benefit,
                function () {
                    return '€' . number_format($this->benefit, 2);
                }
            ),
        ];
    }
}
