<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PurchaseResource;
use App\Http\Traits\ApiResponse;
use App\Models\Purchase;
use App\Models\Appointment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PurchaseController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of purchases
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $appointmentId = $request->get('appointment_id');
        $status = $request->get('status');

        $query = Purchase::with(['appointment.assistant', 'appointment.representative']);

        // Filter by appointment
        if ($appointmentId) {
            $query->where('appointment_id', $appointmentId);
        }

        // Filter by status
        if ($status) {
            $query->where('status', $status);
        }

        // Order by creation date
        $query->orderBy('created_at', 'desc');

        $purchases = $query->paginate($perPage);

        return $this->paginatedResponse(
            PurchaseResource::collection($purchases),
            'Purchases retrieved successfully'
        );
    }

    /**
     * Store a newly created purchase
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'appointment_id' => 'required|exists:appointments,id',
            'status' => 'required|in:purchased,not_purchased',
            'description' => 'nullable|string',
            'item_type' => 'required|string',
            'weight' => 'nullable|numeric|min:0',
            'buy_price' => 'nullable|numeric|min:0',
            'resale_price' => 'nullable|numeric|min:0',
            'benefit' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $purchase = Purchase::create($request->all());
        $purchase->load('appointment');

        return $this->createdResponse(
            new PurchaseResource($purchase),
            'Purchase created successfully'
        );
    }

    /**
     * Display the specified purchase
     */
    public function show(Purchase $purchase): JsonResponse
    {
        $purchase->load('appointment');

        return $this->successResponse(
            new PurchaseResource($purchase),
            'Purchase retrieved successfully'
        );
    }

    /**
     * Update the specified purchase
     */
    public function update(Request $request, Purchase $purchase): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|required|in:purchased,not_purchased',
            'description' => 'nullable|string',
            'item_type' => 'sometimes|required|string',
            'weight' => 'nullable|numeric|min:0',
            'buy_price' => 'nullable|numeric|min:0',
            'resale_price' => 'nullable|numeric|min:0',
            'benefit' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors());
        }

        $purchase->update($request->all());
        $purchase->load('appointment');

        return $this->successResponse(
            new PurchaseResource($purchase),
            'Purchase updated successfully'
        );
    }

    /**
     * Remove the specified purchase
     */
    public function destroy(Purchase $purchase): JsonResponse
    {
        $purchase->delete();

        return $this->successResponse(null, 'Purchase deleted successfully');
    }

    /**
     * Get purchases for a specific appointment
     */
    public function getByAppointment(Appointment $appointment): JsonResponse
    {
        $purchases = $appointment->purchases()->with('appointment')->get();

        return $this->successResponse(
            PurchaseResource::collection($purchases),
            'Appointment purchases retrieved successfully'
        );
    }

    /**
     * Get purchase statistics
     */
    public function getStats(Request $request): JsonResponse
    {
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $query = Purchase::query();

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }
        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        $stats = [
            'total_purchases' => $query->count(),
            'purchased_items' => $query->where('status', 'purchased')->count(),
            'not_purchased_items' => $query->where('status', 'not_purchased')->count(),
            'total_buy_price' => $query->sum('buy_price'),
            'total_resale_price' => $query->sum('resale_price'),
            'total_benefit' => $query->sum('benefit'),
            'average_weight' => $query->avg('weight'),
        ];

        return $this->successResponse($stats, 'Purchase statistics retrieved successfully');
    }
}
