import { Head, <PERSON>, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Users, TrendingUp, Calendar, FileText } from "lucide-react"
import { useState } from "react"






const breadcrumbs = [
    {
        title: 'Tableau de bord',
        href: '/welcomeExecutive',
    }
];


export default function welcomeExecutive() {

    const { auth ,totalAssistants, monthlyTotalBonus, monthYear, bonusesData } = usePage().props
    

    const [selectedMonth, setSelectedMonth] = useState("2024-01")

    const months = [
        { value: "2024-01", label: "Janvier 2024" },
        { value: "2024-02", label: "Février 2024" },
        { value: "2024-03", label: "Mars 2024" },
        { value: "2024-04", label: "Avril 2024" },
        { value: "2024-05", label: "Mai 2024" },
        { value: "2024-06", label: "Juin 2024" },
    ]

    const currentMonthLabel = months.find((m) => m.value === selectedMonth)?.label || "Janvier 2024"

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Welcome" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl lg:p-4">
                <div className="flex-1 p-6">
                    <div className="max-w-7xl mx-auto space-y-6">
                        {/* Dashboard Cards */}
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                            {/* Total Assistants */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Total assistants</CardTitle>
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{totalAssistants && totalAssistants}</div>
                                    <p className="text-xs text-muted-foreground">Assistants commerciaux actifs</p>
                                </CardContent>
                            </Card>

                            {/* Total Bonuses This Month */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Total bonus ce mois</CardTitle>
                                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{monthlyTotalBonus && monthlyTotalBonus} €</div>
                                    <p className="text-xs text-muted-foreground">+15% par rapport au mois dernier</p>
                                </CardContent>
                            </Card>

                            {/* Current Month Selected */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Mois sélectionné</CardTitle>
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{monthYear && monthYear.month}</div>
                                    <p className="text-xs text-muted-foreground">{monthYear && monthYear.year}</p>
                                </CardContent>
                            </Card>

                            {/* Quick Link to Payroll */}
                            <Card className="bg-primary text-primary-foreground">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Accès rapide</CardTitle>
                                    <FileText className="h-4 w-4" />
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <p className="text-sm">Gérer la paie et les exports</p>
                                        <Button asChild variant="secondary" size="sm" className="w-full">
                                            <Link href="/payrollPage">Aller à la paie</Link>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Additional Information Cards */}
                        <div className="grid gap-6 ">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Aperçu des performances</CardTitle>
                                    <CardDescription>Résumé des bonus distribués ce mois</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm">Bonus Standard</span>
                                        <span className="font-semibold">€{bonusesData && bonusesData.standardBonuses}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm">Bonus (Objectives LeBonCoin)</span>
                                        <span className="font-semibold">€{bonusesData && bonusesData.dailyBonuses}</span>
                                    </div>
                                    <div className="border-t pt-2">
                                        <div className="flex justify-between items-center font-semibold">
                                            <span>Total</span>
                                            <span>€{bonusesData && bonusesData.standardBonuses + bonusesData.dailyBonuses}</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
};
