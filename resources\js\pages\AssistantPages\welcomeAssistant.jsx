import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, Clock } from "lucide-react"
import { StatsCard } from "@/components/ui/statsCard"
import { Button } from "@/components/ui/button"

const breadcrumbs = [
    {
        title: 'Tableau de bord',
        href: '/welcomeAssistant',
    },
];

export default function WelcomeAssistant() {

    const { dailyAppointments, weeklyAppointmentsTotal, announcedTodayAppointments, weeklyAnnouncedAppointmentsTotal, auth } = usePage().props
    
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Welcome" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div>
                    <h1 className="text-3xl font-bold text-[#525e62]">Bienvenue, {auth.user?.name}</h1>
                    <p className="text-[#525e62]/70">Voici un aperçu de votre activité en tant qu’assistant commercial.</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <StatsCard
                        title={"Rendez-vous du jour"}
                        value={`${dailyAppointments} / (${announcedTodayAppointments} Announced)`}
                        icon={Calendar}
                        color={"bg-blue-500"}
                    >
                    </StatsCard>

                    <StatsCard
                        title={"Rendez-vous cette semaine"}
                        value={`${weeklyAppointmentsTotal} / (${weeklyAnnouncedAppointmentsTotal} Announced)`}
                        icon={Clock}
                        color={"bg-green-500"}
                    >
                    </StatsCard>
                </div>

                <div className="flex w-full gap-6">
                    <Card className="bg-white border-0 w-full shadow-lg">
                        <CardHeader>
                            <CardTitle className="text-[#525e62]">Actions rapides</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3"  onClick={() => { router.visit("/appointmentCreation") }} >
                                <div className="p-3 bg-[#f1efe0]/50 rounded-lg cursor-pointer hover:bg-[#f1efe0] transition-colors">
                                    <p className="font-medium text-[#525e62]">Nouveau rendez-vous</p>
                                    <p className="text-sm text-[#525e62]/70">
                                        Planifier un rendez-vous client
                                    </p>
                                </div>
                            </div>
                            <div className="space-y-3 mt-5"  onClick={() => { router.visit("/appointmentsHistory") }} >
                                <div className="p-3 bg-[#f1efe0]/50 rounded-lg cursor-pointer hover:bg-[#f1efe0] transition-colors">
                                    <p className="font-medium text-[#525e62]">Historique des rendez-vous</p>
                                    <p className="text-sm text-[#525e62]/70">
                                        Consulter l’historique des rendez-vous
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
