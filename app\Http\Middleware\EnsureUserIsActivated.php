<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserIsActivated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check() && !Auth::user()->is_activated) {
            Auth::logout();

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Votre compte a été désactivé. Veuillez contacter un administrateur.'
                ], 401);
            }

            return redirect('/login')->withErrors([
                'email' => 'Votre compte a été désactivé. Veuillez contacter un administrateur.'
            ]);
        }

        return $next($request);
    }
}
