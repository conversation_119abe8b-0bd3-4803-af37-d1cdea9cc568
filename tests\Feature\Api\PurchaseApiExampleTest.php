<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Purchase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PurchaseApiExampleTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $representative;
    protected $assistant;
    protected $appointment;
    protected $token;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->representative = User::factory()->create([
            'role' => 'representative',
            'is_activated' => true,
        ]);
        
        $this->assistant = User::factory()->create([
            'role' => 'assistant',
            'is_activated' => true,
        ]);
        
        $this->appointment = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);
        
        $this->token = $this->representative->createToken('test-token')->plainTextToken;
    }

    public function test_store_successful_purchase_example()
    {
        // Example: <PERSON> bought a gold necklace from client
        $purchaseData = [
            'appointment_id' => $this->appointment->id,
            'status' => 'purchased',
            'description' => 'Collier en or 18 carats avec pendentif diamant',
            'item_type' => 'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
            'weight' => 25.5,
            'buy_price' => 450.00,
            'resale_price' => 750.00,
            'benefit' => 300.00,
            'notes' => 'Excellent état, pièce authentique vérifiée'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/purchases', $purchaseData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'appointment_id',
                        'status',
                        'description',
                        'item_type',
                        'weight',
                        'buy_price',
                        'resale_price',
                        'benefit',
                        'notes',
                        'created_at',
                        'updated_at',
                        'profit_margin',
                        'is_profitable',
                        'weight_display',
                        'buy_price_formatted',
                        'resale_price_formatted',
                        'benefit_formatted',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'message' => 'Purchase created successfully',
                    'data' => [
                        'appointment_id' => $this->appointment->id,
                        'status' => 'purchased',
                        'description' => 'Collier en or 18 carats avec pendentif diamant',
                        'weight' => 25.5,
                        'buy_price' => 450.00,
                        'resale_price' => 750.00,
                        'benefit' => 300.00,
                        'profit_margin' => 40.0, // (750-450)/750 * 100
                        'is_profitable' => true,
                        'weight_display' => '25.5g',
                        'buy_price_formatted' => '€450.00',
                        'resale_price_formatted' => '€750.00',
                        'benefit_formatted' => '€300.00',
                    ],
                ]);

        // Verify data was stored in database
        $this->assertDatabaseHas('purchases', [
            'appointment_id' => $this->appointment->id,
            'status' => 'purchased',
            'description' => 'Collier en or 18 carats avec pendentif diamant',
            'weight' => 25.5,
            'buy_price' => 450.00,
            'resale_price' => 750.00,
            'benefit' => 300.00,
        ]);
    }

    public function test_store_no_purchase_example()
    {
        // Example: Client declined the offer
        $purchaseData = [
            'appointment_id' => $this->appointment->id,
            'status' => 'not_purchased',
            'description' => 'Bague en argent avec pierre bleue',
            'item_type' => 'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
            'weight' => 8.2,
            'buy_price' => null,
            'resale_price' => 120.00,
            'benefit' => null,
            'notes' => 'Client a décliné l\'offre, souhaite réfléchir'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/purchases', $purchaseData);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Purchase created successfully',
                    'data' => [
                        'appointment_id' => $this->appointment->id,
                        'status' => 'not_purchased',
                        'description' => 'Bague en argent avec pierre bleue',
                        'weight' => 8.2,
                        'buy_price' => null,
                        'resale_price' => 120.00,
                        'benefit' => null,
                        'notes' => 'Client a décliné l\'offre, souhaite réfléchir',
                    ],
                ]);

        $this->assertDatabaseHas('purchases', [
            'appointment_id' => $this->appointment->id,
            'status' => 'not_purchased',
            'description' => 'Bague en argent avec pierre bleue',
            'buy_price' => null,
            'benefit' => null,
        ]);
    }

    public function test_store_multiple_purchases_example()
    {
        // Example: Multiple items evaluated in one appointment
        $items = [
            [
                'appointment_id' => $this->appointment->id,
                'status' => 'purchased',
                'description' => 'Montre Rolex Submariner',
                'item_type' => 'montres',
                'weight' => 150.0,
                'buy_price' => 2500.00,
                'resale_price' => 4200.00,
                'benefit' => 1700.00,
                'notes' => 'Authentique, bon état général'
            ],
            [
                'appointment_id' => $this->appointment->id,
                'status' => 'not_purchased',
                'description' => 'Bracelet en or blanc',
                'item_type' => 'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
                'weight' => 12.3,
                'buy_price' => null,
                'resale_price' => 180.00,
                'benefit' => null,
                'notes' => 'Client souhaite garder pour l\'instant'
            ]
        ];

        $createdPurchases = [];

        foreach ($items as $item) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
            ])->postJson('/api/v1/purchases', $item);

            $response->assertStatus(201);
            $createdPurchases[] = $response->json('data');
        }

        // Verify both purchases were created
        $this->assertCount(2, $createdPurchases);
        
        // Verify first purchase (successful)
        $this->assertEquals('purchased', $createdPurchases[0]['status']);
        $this->assertEquals(2500.00, $createdPurchases[0]['buy_price']);
        $this->assertTrue($createdPurchases[0]['is_profitable']);

        // Verify second purchase (declined)
        $this->assertEquals('not_purchased', $createdPurchases[1]['status']);
        $this->assertNull($createdPurchases[1]['buy_price']);

        // Verify database records
        $this->assertDatabaseCount('purchases', 2);
    }

    public function test_purchase_validation_example()
    {
        // Example: Missing required fields
        $invalidData = [
            'appointment_id' => $this->appointment->id,
            // Missing status and item_type
            'description' => 'Some item',
            'weight' => 10.0,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/purchases', $invalidData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed',
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'errors' => [
                        'status',
                        'item_type',
                    ],
                ]);
    }

    public function test_purchase_with_nonexistent_appointment()
    {
        // Example: Trying to create purchase for non-existent appointment
        $purchaseData = [
            'appointment_id' => 99999, // Non-existent appointment
            'status' => 'purchased',
            'description' => 'Some item',
            'item_type' => 'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
            'buy_price' => 100.00,
            'resale_price' => 150.00,
            'benefit' => 50.00,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/purchases', $purchaseData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed',
                ])
                ->assertJsonStructure([
                    'errors' => [
                        'appointment_id',
                    ],
                ]);
    }
}
