import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { AppointmentForm } from "@/components/appointmentForm.jsx"

const breadcrumbs = [
    {
        title: 'Création de rendez-vous',
        href: '/appointmentCreation',
    },
];

export default function AppointmentCreation() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Création de Rendez-vous" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    <div>
                        <h1 className="text-3xl font-bold text-[#525e62]">Gestion des Rendez-vous</h1>
                        <p className="text-[#525e62]/70">Planifiez et suivez vos rendez-vous clients.</p>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                        <AppointmentForm />
                    </div>
                </div>
            </div>
        </AppLayout>
    );
};

