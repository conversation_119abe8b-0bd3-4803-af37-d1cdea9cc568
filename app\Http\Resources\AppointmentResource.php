<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'assistant_id' => $this->assistant_id,
            'representative_id' => $this->representative_id,
            'client_name' => $this->clientName,
            'client_phone' => $this->clientPhone,
            'client_address' => $this->clientAddress,
            'source' => $this->source,
            'date_time' => $this->dateTime?->toISOString(),
            'notes' => $this->notes,
            'items_collection' => $this->itemsCollection,
            'appointment_type' => $this->appointment_type,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'assistant' => new UserResource($this->whenLoaded('assistant')),
            'representative' => new UserResource($this->whenLoaded('representative')),
            'purchases' => PurchaseResource::collection($this->whenLoaded('purchases')),
            'purchase' => new PurchaseResource($this->whenLoaded('purchase')),
            'bonuses' => BonusResource::collection($this->whenLoaded('bonuses')),

            // Counts
            'purchases_count' => $this->whenCounted('purchases'),
            'bonuses_count' => $this->whenCounted('bonuses'),

            // Computed fields for mobile app convenience
            'status' => $this->when(
                $this->relationLoaded('purchases'),
                function () {
                    return $this->purchases->isEmpty() ? 'pending' : 'filled';
                }
            ),

            'total_purchase_amount' => $this->when(
                $this->relationLoaded('purchases'),
                function () {
                    return $this->purchases->sum('buy_price');
                }
            ),
            'total_estimated_value' => $this->when(
                $this->relationLoaded('purchases'),
                function () {
                    return $this->purchases->sum('resale_price');
                }
            ),
            'total_benefit' => $this->when(
                $this->relationLoaded('purchases'),
                function () {
                    return $this->purchases->sum('benefit');
                }
            ),
        ];
    }
}
