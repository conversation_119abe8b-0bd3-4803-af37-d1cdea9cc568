import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { Clock, MapPin, Phone, User } from 'lucide-react';
import { useState } from 'react';
import { PostRDVModal } from './partials/postRdv';

export default function CalendarPage() {
    const { appointments: backendAppointments } = usePage().props;
    const breadcrumbs = [
        {
            title: 'Calendrier des rendez-vous',
            href: '/representative/calendar',
        },
    ];
    const [selectedDate, setSelectedDate] = useState(() => {
        const date = new Date();
        date.setHours(0, 0, 0, 0);
        return date;
    });
    const [selectedAppointment, setSelectedAppointment] = useState(null);
    const [showPostRDV, setShowPostRDV] = useState(false);

    const getStatusColor = (status) => {
        switch (status) {
            case 'confirmé':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'en-route':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'terminé':
                return 'bg-gray-100 text-gray-800 border-gray-200';
            case 'annulé':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        }
    };

    const getTypeColor = (type) => {
        switch (type) {
            case 'Or':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'Montre':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'Brocante':
                return 'bg-green-100 text-green-800 border-green-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const formatDate = (date) => {
        if (!date) return '';
        // Ensure we're working with a Date object
        const d = new Date(date);
        // Returns 'YYYY-MM-DD' from a Date object
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const todayAppointments = backendAppointments.filter((apt) => {
        if (!apt.dateTime) return false;

        // Format both dates consistently
        const aptDate = formatDate(apt.dateTime);
        const selectedDateFormatted = formatDate(selectedDate);

        return aptDate === selectedDateFormatted;
    });

    const handleDateSelect = (e) => {
        // Create a new date object to ensure state change
        const newDate = new Date(e.target.value);
        newDate.setHours(0, 0, 0, 0);
        setSelectedDate(newDate);
        // Reset selected appointment when changing dates
        setSelectedAppointment(null);
        // }
    };
    const goToNextApt = () => {
        const idx = backendAppointments.findIndex((apt) => apt.id === selectedAppointment.id);
        setSelectedAppointment(backendAppointments[idx + 1] || backendAppointments[0]);
    };
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Calendrier des rendez-vous" />
            <div className="space-y-6 p-3 lg:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-[#525e62]">Calendrier des Rendez-vous</h1>
                        <p className="text-[#525e62]/70">Gérez vos rendez-vous et consultez votre planning.</p>
                    </div>
                    {/* <Button className="bg-[#525e62] text-white hover:bg-[#525e62]/90">
                        <Plus className="mr-2 h-4 w-4" />
                        Nouveau RDV
                    </Button> */}
                </div>

                <Card className="border-0 bg-white shadow-lg">
                    <CardContent>
                        {/* <Calendar 
                                mode="single" 
                                selected={selectedDate} 
                                onSelect={handleDateSelect} 
                                className="rounded-md border" 
                            /> */}
                        <input
                            type="date"
                            value={formatDate(selectedDate)}
                            onChange={handleDateSelect}
                            className="w-full rounded-md border border-[#525e62]/20 p-2 focus:border-[#525e62] focus:outline-none"
                        />
                    </CardContent>
                </Card>
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Mini Calendrier */}

                    {/* Liste des RDV du jour */}
                    <Card className="border-0 bg-white shadow-lg">
                        <CardHeader>
                            <CardTitle className="text-[#525e62]">
                                RDV du{' '}
                                {selectedDate.toLocaleDateString('fr-FR', {
                                    weekday: 'long',
                                    day: 'numeric',
                                    month: 'long',
                                })}
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {todayAppointments.length > 0 ? (
                                    todayAppointments.map((appointment, index) => (
                                        <div
                                            key={index}
                                            className={`cursor-pointer rounded-lg border-2 p-4 transition-all hover:shadow-md ${
                                                selectedAppointment?.id === appointment.id
                                                    ? 'border-[#525e62] bg-[#525e62]/5 shadow-md'
                                                    : 'border-[#525e62]/10 hover:border-[#525e62]/30'
                                            }`}
                                            onClick={() => setSelectedAppointment(appointment)}
                                        >
                                            <div className="mb-2 flex items-center justify-between">
                                                <span className="text-lg font-bold text-[#525e62]">
                                                    {new Date(appointment.dateTime).toLocaleTimeString('fr-FR', {
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                    })}
                                                </span>
                                                <Badge className={getStatusColor(appointment.status)}>
                                                    {appointment.appointment_type === 'announced' ? 'annoncé' : 'non annoncé'}
                                                </Badge>
                                            </div>
                                            <p className="mb-1 font-medium text-[#525e62]">{appointment.clientName}</p>

                                            <p className="mt-2 text-xs text-[#525e62]/70">{appointment.estimatedValue}</p>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-center text-[#525e62]/70">Aucun rendez-vous prévu pour cette date</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Détail du RDV sélectionné */}
                    {selectedAppointment && (
                        <Card className="border-0 bg-white shadow-lg lg:col-span-2">
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between text-[#525e62]">
                                    <span className="flex items-center">
                                        <User className="mr-2 h-5 w-5" />
                                        Détails du Rendez-vous
                                    </span>
                                    {new Date(selectedAppointment.dateTime) >= new Date() && selectedAppointment?.purchases.length === 0 && (
                                        <div className="flex space-x-2">
                                            <Button
                                                size="sm"
                                                className="bg-[#525e62] text-white hover:bg-[#525e62]/90"
                                                onClick={() => setShowPostRDV(true)}
                                            >
                                                Post-RDV
                                            </Button>
                                        </div>
                                    )}
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="space-y-4">
                                            <div className="rounded-lg bg-[#f1efe0]/30 p-4">
                                                <h3 className="mb-3 font-medium text-[#525e62]">Informations Client</h3>
                                                <div className="space-y-2">
                                                    <p className="text-lg font-bold text-[#525e62]">{selectedAppointment.clientName}</p>
                                                    <div className="flex items-center space-x-2 text-[#525e62]/70">
                                                        <Phone className="h-4 w-4" />
                                                        <span>{selectedAppointment.clientPhone}</span>
                                                    </div>
                                                    <p className="text-sm text-[#525e62]/70">{selectedAppointment.clientEmail}</p>
                                                </div>
                                            </div>

                                            <div className="rounded-lg bg-blue-50 p-4">
                                                <h3 className="mb-3 font-medium text-[#525e62]">Détails RDV</h3>
                                                <div className="space-y-2">
                                                    <div className="flex items-center space-x-2">
                                                        <Clock className="h-4 w-4 text-[#525e62]/70" />
                                                        <span className="font-medium text-[#525e62]">
                                                            {new Date(selectedAppointment.dateTime).toLocaleTimeString('fr-FR', {
                                                                hour: '2-digit',
                                                                minute: '2-digit',
                                                            })}
                                                        </span>
                                                    </div>
                                                    {/* <Badge className={getTypeColor(selectedAppointment.type)}>
                                                        {selectedAppointment.type}
                                                    </Badge> */}
                                                    <Badge className={getStatusColor(selectedAppointment.status)}>{selectedAppointment.source  === 'outbound' ? 'sortant' : 'Leboncoin'}</Badge>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="space-y-4">
                                            <div className="rounded-lg bg-green-50 p-4">
                                                <h3 className="mb-3 font-medium text-[#525e62]">Localisation</h3>
                                                <div className="flex items-start space-x-2">
                                                    <MapPin className="mt-1 h-4 w-4 text-[#525e62]/70" />
                                                    <span className="text-[#525e62]">{selectedAppointment.clientAddress}</span>
                                                </div>
                                            </div>

                                            {/* <div className="rounded-lg bg-yellow-50 p-4">
                                                <h3 className="mb-3 font-medium text-[#525e62]">Estimation</h3>
                                                <p className="text-lg font-bold text-[#525e62]">{selectedAppointment.estimatedValue}</p>
                                            </div> */}
                                        </div>
                                    </div>
                                    <div>
                                        {selectedAppointment.itemsCollection.map((item, index) => (
                                            <Badge key={index} className={`whitespace-pre-line ${getTypeColor(item)}`}>
                                                {item}
                                            </Badge>
                                        ))}
                                    </div>
                                    <div className="rounded-lg bg-gray-50 p-4">
                                        <h3 className="mb-2 font-medium text-[#525e62]">Notes</h3>
                                        <p className="text-[#525e62]/80">{selectedAppointment.notes}</p>
                                    </div>

                                    {/* <div className="flex flex-wrap gap-3 border-t border-[#525e62]/10 pt-4">
                                        <Button className="bg-[#525e62] text-white hover:bg-[#525e62]/90">Démarrer le RDV</Button>
                                        <Button variant="outline" className="border-[#525e62] text-[#525e62]">
                                            Appeler le client
                                        </Button>
                                        <Button variant="outline" className="border-[#525e62] text-[#525e62]">
                                            Voir l'itinéraire
                                        </Button>
                                        <Button variant="outline" className="border-[#525e62] text-[#525e62]">
                                            Modifier RDV
                                        </Button>
                                    </div> */}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Modal Post-RDV */}
                {selectedAppointment && (
                    <PostRDVModal isOpen={showPostRDV} onClose={() => setShowPostRDV(false)} appointment={selectedAppointment} next={goToNextApt} />
                )}
            </div>
        </AppLayout>
    );
}
