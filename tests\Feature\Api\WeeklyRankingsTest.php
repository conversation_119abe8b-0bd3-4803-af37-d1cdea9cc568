<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Purchase;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class WeeklyRankingsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set a fixed date for consistent testing
        Carbon::setTestNow(Carbon::parse('2024-08-22 10:00:00')); // Thursday
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow(); // Reset Carbon
        parent::tearDown();
    }

    /** @test */
    public function it_returns_weekly_rankings_for_authenticated_user()
    {
        // Create a test user for authentication
        $user = User::factory()->create(['role' => 'assistant']);
        Sanctum::actingAs($user);

        // Create representatives and assistant
        $assistant = User::factory()->create(['role' => 'assistant', 'is_activated' => true]);
        $rep1 = User::factory()->create(['role' => 'representative', 'is_activated' => true]);
        $rep2 = User::factory()->create(['role' => 'representative', 'is_activated' => true]);

        // Create appointments for this week
        $weekStart = Carbon::now()->startOfWeek(Carbon::MONDAY);
        $appointmentDate = $weekStart->copy()->addDays(1); // Tuesday

        $appointment1 = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $rep1->id,
            'dateTime' => $appointmentDate,
        ]);

        $appointment2 = Appointment::factory()->create([
            'assistant_id' => $assistant->id,
            'representative_id' => $rep2->id,
            'dateTime' => $appointmentDate,
        ]);

        // Create purchases for completed appointments
        Purchase::factory()->create([
            'appointment_id' => $appointment1->id,
            'status' => 'purchased',
            'resale_price' => 1000,
        ]);

        // Make the API request
        $response = $this->getJson('/api/v1/representatives/weekly-rankings');

        // Assert the response structure
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'rankings' => [
                            '*' => [
                                'rank',
                                'representative' => [
                                    'id',
                                    'name',
                                    'email',
                                    'initials',
                                    'joined_date'
                                ],
                                'performance' => [
                                    'total_appointments',
                                    'completed_appointments',
                                    'completion_rate',
                                    'weekly_revenue'
                                ],
                                'badges' => [
                                    'top_performer',
                                    'rank_badge'
                                ]
                            ]
                        ],
                        'meta' => [
                            'week_period' => [
                                'start',
                                'end',
                                'week_number',
                                'year'
                            ],
                            'total_representatives',
                            'generated_at'
                        ]
                    ]
                ]);

        // Assert success response
        $response->assertJson([
            'success' => true,
            'message' => 'Weekly team rankings retrieved successfully'
        ]);
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->getJson('/api/v1/representatives/weekly-rankings');
        
        $response->assertStatus(401);
    }

    /** @test */
    public function it_orders_representatives_by_completed_appointments()
    {
        $user = User::factory()->create(['role' => 'assistant']);
        Sanctum::actingAs($user);

        // Create representatives and assistant
        $assistant = User::factory()->create(['role' => 'assistant', 'is_activated' => true]);
        $rep1 = User::factory()->create(['role' => 'representative', 'is_activated' => true, 'name' => 'Rep One']);
        $rep2 = User::factory()->create(['role' => 'representative', 'is_activated' => true, 'name' => 'Rep Two']);

        $weekStart = Carbon::now()->startOfWeek(Carbon::MONDAY);
        $appointmentDate = $weekStart->copy()->addDays(1);

        // Rep1: 2 appointments, 2 completed
        $app1 = Appointment::factory()->create(['assistant_id' => $assistant->id, 'representative_id' => $rep1->id, 'dateTime' => $appointmentDate]);
        $app2 = Appointment::factory()->create(['assistant_id' => $assistant->id, 'representative_id' => $rep1->id, 'dateTime' => $appointmentDate]);
        Purchase::factory()->create(['appointment_id' => $app1->id, 'status' => 'purchased', 'resale_price' => 500]);
        Purchase::factory()->create(['appointment_id' => $app2->id, 'status' => 'purchased', 'resale_price' => 500]);

        // Rep2: 1 appointment, 1 completed
        $app3 = Appointment::factory()->create(['assistant_id' => $assistant->id, 'representative_id' => $rep2->id, 'dateTime' => $appointmentDate]);
        Purchase::factory()->create(['appointment_id' => $app3->id, 'status' => 'purchased', 'resale_price' => 1000]);

        $response = $this->getJson('/api/v1/representatives/weekly-rankings');

        $rankings = $response->json('data.rankings');
        
        // Rep1 should be first (2 completed appointments)
        $this->assertEquals(1, $rankings[0]['rank']);
        $this->assertEquals($rep1->name, $rankings[0]['representative']['name']);
        $this->assertEquals(2, $rankings[0]['performance']['completed_appointments']);
        
        // Rep2 should be second (1 completed appointment)
        $this->assertEquals(2, $rankings[1]['rank']);
        $this->assertEquals($rep2->name, $rankings[1]['representative']['name']);
        $this->assertEquals(1, $rankings[1]['performance']['completed_appointments']);
    }
}
