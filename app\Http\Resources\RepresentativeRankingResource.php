<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RepresentativeRankingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'rank' => $this->rank,
            'representative' => [
                'id' => $this->id,
                'name' => $this->name,
                'email' => $this->email,
                'initials' => $this->getInitials(),
                'joined_date' => $this->created_at?->toDateString(),
            ],
            'performance' => [
                'total_appointments' => (int) $this->total_appointments,
                'completed_appointments' => (int) $this->completed_appointments,
                'completion_rate' => (float) $this->completion_rate,
                'weekly_revenue' => (float) $this->weekly_revenue,
            ],
            'badges' => [
                'top_performer' => $this->rank <= 3,
                'rank_badge' => $this->getRankBadge(),
            ],
        ];
    }

    /**
     * Get user initials from name
     */
    private function getInitials(): string
    {
        $words = explode(' ', trim($this->name));
        $initials = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }
        
        return substr($initials, 0, 2); // Limit to 2 characters
    }

    /**
     * Get rank badge emoji
     */
    private function getRankBadge(): string
    {
        switch ($this->rank) {
            case 1:
                return '🥇';
            case 2:
                return '🥈';
            case 3:
                return '🥉';
            default:
                return '';
        }
    }
}
