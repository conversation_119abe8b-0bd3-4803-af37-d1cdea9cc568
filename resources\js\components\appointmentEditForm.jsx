import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"

import { X, Save, User, Calendar, Clock, Plus } from "lucide-react"
import { useForm } from "@inertiajs/react"
import dayjs from "dayjs"

export default function AppointmentEditForm({ appointment, onClose, salesReps }) {

    const updatable = (appointment) => {
        const appDate = dayjs(appointment.dateTime)
        const now = dayjs();
        const isBefore = now.isBefore(appDate.subtract(2, "hour"))
        return isBefore
    }

    const { data, setData, put, processing, errors } = useForm({
        id: appointment.id,
        clientName: appointment.clientName,
        clientPhone: appointment.clientPhone,
        clientAddress: appointment.clientAddress,
        appointment_type: appointment.appointment_type,
        dateTime: appointment.dateTime,
        itemsCollection: appointment.itemsCollection,
        notes: appointment.notes,
        source: appointment.source,
    })

    useEffect(() => {
        if (appointment) {
            setData({ ...appointment })
        }
    }, [appointment])

    const handleEdit = (e) => {
        e.preventDefault()
        if (updatable(appointment)) {
            put(route("appointments.update", data.id), {
                onSuccess: () => onClose()
            })
        } else {
            alert("vous ne pouvez plus modifier le rendez-vous")
        }
    }

    let selectedItems = appointment.itemsCollection;

    const items = [
        "lustres anciens, chandelier",
        "pendule, horloge comtoise",
        "montre et montre a gousset (en argent et en or)",
        "gueridon, sellette, coiffeuse",
        "vaisselle ancienne (porcelaine, cuivre, etain, cristal)",
        "tableaux anciens",
        "livres anciens (missels etc)",
        "manteaux de fourrure veritable",
        "toque, etole, echarpe en fourrure",
        "bijoux de fantaisie",
        "bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)",
        "accessoires anciens (boutons de manchette, pince a cravate, broche, epingle a chapeau, came)",
        "art asiatique (vase chinois)",
        "pieces de monnaie (etrangeres, en or ou franc)",
        "machine a coudre"
    ];

    const toggleItem = (item) => {
        let itemIndex

        if (selectedItems.includes(item)) {
            itemIndex = selectedItems.indexOf(item);
            selectedItems.splice(itemIndex, 1)
        } else {
            selectedItems.push(item);
        }
        setData({ ...data, itemsCollection: selectedItems })
    };

    let appointmentRep = salesReps.find((rep) => rep.id == appointment.representative_id)

    return (
        <Dialog open={!!appointment} onOpenChange={() => onClose()}>
            <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="text-[#525e62] flex items-center">
                        <User className="mr-2 h-5 w-5" />
                        Modifier le rendez-vous
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleEdit} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="clientName" className="text-[#525e62]">Nom du client</Label>
                        <Input
                            readOnly={!updatable(appointment)}
                            id="clientName"
                            value={data.clientName}
                            onChange={(e) => setData({ ...data, clientName: e.target.value })}
                            className="border-[#525e62]/20 focus:border-[#525e62]"
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="phone" className="text-[#525e62]">Téléphone</Label>
                        <Input
                            readOnly={!updatable(appointment)}
                            id="phone"
                            value={data.clientPhone}
                            onChange={(e) => setData({ ...data, clientPhone: e.target.value })}
                            className="border-[#525e62]/20 focus:border-[#525e62]"
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="address" className="text-[#525e62]">Adresse du client</Label>
                        <Input
                            readOnly={!updatable(appointment)}
                            id="address"
                            value={data.clientAddress}
                            onChange={(e) => setData({ ...data, clientAddress: e.target.value })}
                            className="border-[#525e62]/20 focus:border-[#525e62]"
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="source" className="text-[#525e62]">Source</Label>
                        {
                            updatable(appointment) ?
                                <>
                                    <Select value={data.source} onValueChange={(value) => setData({ ...data, source: value })}>
                                        <SelectTrigger className="border-[#525e62]/20 focus:border-[#525e62]">
                                            <SelectValue placeholder="Sélectionner la source" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="outbound">Appel sortant</SelectItem>
                                            <SelectItem value="leboncoin">LeBonCoin</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </>
                                :
                                <>
                                    <Input
                                        readOnly
                                        id="source"
                                        value={data.source}
                                        className="border-[#525e62]/20 focus:border-[#525e62]"
                                    />
                                </>
                        }
                    </div>

                    <div className="grid grid-cols-1  gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="date" className="text-[#525e62]">Date</Label>
                            <div className="flex items-center space-x-2">
                                <Calendar className="h-4 w-4 text-[#525e62]/70" />
                                <Input
                                    id="date"
                                    type="dateTime-Local"
                                    readOnly={!updatable(appointment)}
                                    value={data.dateTime}
                                    onChange={(e) => setData({ ...data, dateTime: e.target.value })}
                                    className="border-[#525e62]/20 w-full focus:border-[#525e62]"
                                    required
                                />
                            </div>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="status" className="text-[#525e62]">Type de rendez-vous</Label>
                        {
                            updatable(appointment) ?
                                <>
                                    <Select value={data.appointment_type} onValueChange={(value) => setData({ ...data, appointment_type: value })}>
                                        <SelectTrigger className="border-[#525e62]/20 focus:border-[#525e62]">
                                            <SelectValue placeholder="Sélectionner le type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="announced">Annoncé</SelectItem>
                                            <SelectItem value="not_announced">Non annoncé</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </>
                                :
                                <>
                                    <Input
                                        readOnly
                                        id="source"
                                        value={data.appointment_type}
                                        className="border-[#525e62]/20 focus:border-[#525e62]"
                                    />
                                </>
                        }
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="representative_id" className="text-[#525e62]">
                            Assigné à
                        </Label>
                        {salesReps && salesReps.length > 0 && (
                            <p className="text-xs text-gray-500">
                                Représentants assignés à votre compte ({salesReps.length})
                            </p>
                        )}
                        {
                            updatable(appointment) ?
                                <>
                                    <Select
                                        value={data.representative_id}
                                        onValueChange={(value) => setData({ ...data, representative_id: Number(value) })}
                                    >
                                        <SelectTrigger className="border-[#525e62]/20 focus:border-[#525e62]">
                                            <SelectValue placeholder="Sélectionner un commercial" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {salesReps && salesReps.length > 0 ? (
                                                salesReps.map((rep) => (
                                                    <SelectItem key={rep.id} value={Number(rep.id)}>
                                                        {rep.name}
                                                    </SelectItem>
                                                ))
                                            ) : (
                                                <SelectItem value={0} disabled>
                                                    Aucun représentant assigné
                                                </SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                </>
                                :
                                <Input
                                    readOnly
                                    id="representative_id"
                                    value={appointmentRep.name}
                                    className="border-[#525e62]/20 focus:border-[#525e62]"
                                />
                        }
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="items" className="text-[#525e62]">
                            Objets
                        </Label>
                        <div className="flex space-x-2">
                            <div className="flex flex-wrap gap-2 mb-4">
                                {
                                    items.map((item) => {
                                        const isSelected = appointment.itemsCollection.includes(item);
                                        return (
                                            <button
                                                type="button"
                                                key={item}
                                                onClick={() => toggleItem(item)}
                                                className={`text-sm px-3 py-1 rounded transition ${isSelected ? 'bg-green-200 text-green-900' : 'bg-gray-100 hover:bg-gray-200'}`}
                                            >
                                                {item}
                                            </button>
                                        );
                                    })
                                }
                            </div>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="notes" className="text-[#525e62]">
                            Remarques
                        </Label>
                        <Textarea
                            id="notes"
                            value={data.notes}
                            readOnly={!updatable(appointment)}
                            onChange={(e) => setData({ ...data, notes: e.target.value })}
                            className="border-[#525e62]/20 focus:border-[#525e62]"
                            rows={3}
                            placeholder="Notes supplémentaires sur le rendez-vous"
                        />
                    </div>

                    <div className="flex justify-end space-x-2 pt-4">
                        <Button type="button" variant="outline" onClick={onClose} className="border-[#525e62] text-[#525e62]">
                            <X className="mr-2 h-4 w-4" />
                            Annuler
                        </Button>
                        <Button type={updatable(appointment) ? "submit" : "button"} className="bg-[#525e62] hover:bg-[#525e62]/90 text-white">
                            <Save className="mr-2 h-4 w-4" />
                            Sauvegarder
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    )
}
