<?php

namespace App\Http\Controllers;

use App\Exports\BonusesExport;
use App\Models\Bonus;
use App\Models\Purchase;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class ExecutiveController extends Controller
{
    public function welcomeExecutive()
    {
        try {
            $totalAssistants = User::where("role", "assistant")->count();

            $monthlyTotalBonus = Bonus::whereBetween("created_at", [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])->sum("amount");

            Carbon::setLocale('fr');
            $now = Carbon::now();
            $thisMonth = $now->translatedFormat("F");
            $monthYear = [
                "month" => $thisMonth,
                "year" => $now->year
            ];

            $standardBonuses = Bonus::where("type", "STANDARD")->whereBetween("created_at", [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])->sum("amount");
            $dailyBonuses = Bonus::where("type", "DAILY_SUPP")->whereBetween("created_at", [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])->sum("amount");
            $bonusesData = [
                "standardBonuses" => $standardBonuses,
                "dailyBonuses" => $dailyBonuses
            ];

            return Inertia::render(
                "ExecutivePages/welcomeExecutive",
                [
                    "totalAssistants" => $totalAssistants,
                    "monthlyTotalBonus" => $monthlyTotalBonus,
                    "monthYear" => $monthYear,
                    "bonusesData" => $bonusesData
                ]
            );
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement du tableau de bord exécutif', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement du tableau de bord');
        }
    }
    public function payrollPage(Request $request)
    {
        try {
            $monthlyTotalBonus = Bonus::whereBetween("created_at", [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])->sum("amount");
            $month = $request->query('month'); // e.g. '2025-07'

            $carbon = $month
                ? Carbon::createFromFormat('Y-m', $month)
                : Carbon::now();

            $startOfMonth = $carbon->copy()->startOfMonth();
            $endOfMonth = $carbon->copy()->endOfMonth();
            $monthlyTotalBonus = Bonus::whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->sum('amount');

            $topAssistantData = User::where('role', 'assistant')
                ->withSum(['bonuses' => function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->whereBetween('created_at', [$startOfMonth, $endOfMonth]);
                }], 'amount')
                ->orderByDesc('bonuses_sum_amount')
                ->first();
            $topAssistant = [
                "name" => $topAssistantData->bonuses_sum_amount <= 0 ? "" :$topAssistantData->name,
                "bonus" => $topAssistantData->bonuses_sum_amount
            ];

            $assistantsData = User::where("role", "assistant")
                ->withCount(['bonuses as standardBonus' => function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where("type", "STANDARD")->whereBetween("created_at", [$startOfMonth, $endOfMonth]);
                }])
                ->withCount(['bonuses as lbcBonus' => function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where("type", "DAILY_SUPP")->whereBetween("created_at", [$startOfMonth, $endOfMonth]);
                }])
                ->withSum(['bonuses as standardBonusAmount' => function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where("type", "STANDARD")
                        ->whereBetween("created_at", [$startOfMonth, $endOfMonth]);
                }], 'amount')
                ->withSum(['bonuses as lbcBonusAmount' => function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where("type", "DAILY_SUPP")
                        ->whereBetween("created_at", [$startOfMonth, $endOfMonth]);
                }], 'amount')
                ->get();

                $totalStandardBonus = Bonus::where("type","STANDARD")->whereBetween("created_at",[$startOfMonth, $endOfMonth])->sum("amount");
                $totalLBCBonus = Bonus::where("type","DAILY_SUPP")->whereBetween("created_at",[$startOfMonth, $endOfMonth])->sum("amount");
                $totalBonuses = Bonus::whereBetween("created_at",[$startOfMonth, $endOfMonth])->count();

                $bonusStats = [
                    "totalStandardBonus" => $totalStandardBonus,
                    "totalLBCBonus" => $totalLBCBonus,
                    "totalBonuses" => $totalBonuses
                ];

            return Inertia::render("ExecutivePages/payrollPage", [
                'thisSelectedMonth' => $month ? $month : Carbon::now()->format('Y-m'),
                "monthlyTotalBonus" => $monthlyTotalBonus,
                "topAssistant" => $topAssistant,
                "assistantsData" => $assistantsData,
                "bonusStats" => $bonusStats
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement de la page de paie', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement de la page de paie');
        }
    }
    public function exportBonuses(Request $request)
    {
        try {
            $month = $request->query('month', Carbon::now()->format('Y-m'));
            $year = $request->query('year', Carbon::now()->format('Y'));
            return Excel::download(new BonusesExport($month, $year), 'bonuses' . $month . '_' . $year . '_' . '.xlsx');
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'exportation des bonus', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors de l\'exportation des bonus');
        }
    }
}
