<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Purchase;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class RecruiterController extends Controller
{
    public function dashboard()
    {
        try {
            $now = Carbon::now();
            $weekStart = $now->copy()->startOfWeek()->format('Y-m-d H:i:s');
            $weekEnd = $now->copy()->endOfWeek()->format('Y-m-d H:i:s');
            $monthStart = $now->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $monthEnd = $now->copy()->endOfMonth()->format('Y-m-d H:i:s');

            // Sales Assistants Performance
            $assistantsData = $this->getAssistantsPerformance($weekStart, $weekEnd);

            // Sales Representatives Performance
            $representativesData = $this->getRepresentativesPerformance($weekStart, $weekEnd);

            // Weekly Pairing Insights
            $pairingInsights = $this->getPairingInsights($weekStart, $weekEnd);

            // Performance trends (last 4 weeks)
            $performanceTrends = $this->getPerformanceTrends();

            return Inertia::render('RecruiterPages/Dashboard', [
                'assistantsData' => $assistantsData,
                'representativesData' => $representativesData,
                'pairingInsights' => $pairingInsights,
                'performanceTrends' => $performanceTrends,
                'weekPeriod' => [
                    'start' => Carbon::parse($weekStart)->format('d M'),
                    'end' => Carbon::parse($weekEnd)->format('d M Y')
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement du tableau de bord recruteur', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement du tableau de bord');
        }
    }

    public function performance()
    {
        try {
            $now = Carbon::now();
            $weekStart = $now->copy()->startOfWeek()->format('Y-m-d H:i:s');
            $weekEnd = $now->copy()->endOfWeek()->format('Y-m-d H:i:s');

            // Sales Assistants Performance
            $assistantsData = $this->getAssistantsPerformance($weekStart, $weekEnd);

            // Sales Representatives Performance
            $representativesData = $this->getRepresentativesPerformance($weekStart, $weekEnd);

            // Performance trends (last 4 weeks)
            $performanceTrends = $this->getPerformanceTrends();

            return Inertia::render('RecruiterPages/Performance', [
                'assistantsData' => $assistantsData,
                'representativesData' => $representativesData,
                'performanceTrends' => $performanceTrends,
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des performances', ['error' => $e->getMessage()]);
            return back()->with('error', 'Erreur lors du chargement des performances');
        }
    }

    public function pairings()
    {
        $now = Carbon::now();
        $weekStart = $now->copy()->startOfWeek()->format('Y-m-d H:i:s');
        $weekEnd = $now->copy()->endOfWeek()->format('Y-m-d H:i:s');

        // Current week pairings
        $pairingInsights = $this->getPairingInsights($weekStart, $weekEnd);

        // Pairing effectiveness analysis
        $pairingAnalysis = $this->getPairingEffectivenessAnalysis();

        return Inertia::render('RecruiterPages/Pairings', [
            'pairingInsights' => $pairingInsights,
            'pairingAnalysis' => $pairingAnalysis,
        ]);
    }

    private function getAssistantsPerformance($weekStart, $weekEnd)
    {
        $assistants = User::where('role', 'assistant')
            ->get()
            ->map(function ($assistant) use ($weekStart, $weekEnd) {
                $totalAppointments = Appointment::where('assistant_id', $assistant->id)
                    ->whereBetween('created_at', [$weekStart, $weekEnd])
                    ->count();

                // Since there's no status column, we'll consider appointments with purchases as "validated"
                $validatedAppointments = Appointment::where('assistant_id', $assistant->id)
                    ->whereBetween('created_at', [$weekStart, $weekEnd])
                    ->whereHas('purchase', function ($query) {
                        $query->where('status', 'purchased');
                    })
                    ->count();

                $validationRate = $totalAppointments > 0
                    ? round(($validatedAppointments / $totalAppointments) * 100, 1)
                    : 0;

                return [
                    'id' => $assistant->id,
                    'name' => $assistant->name,
                    'email' => $assistant->email,
                    'total_appointments' => $totalAppointments,
                    'validated_appointments' => $validatedAppointments,
                    'validation_rate' => $validationRate,
                    'avatar' => $this->generateInitials($assistant->name),
                ];
            })
            ->sortByDesc('validated_appointments')
            ->values();

        return [
            'total_assistants' => $assistants->count(),
            'total_appointments' => $assistants->sum('total_appointments'),
            'total_validated' => $assistants->sum('validated_appointments'),
            'avg_validation_rate' => $assistants->avg('validation_rate'),
            'leaderboard' => $assistants->take(5),
            'all_assistants' => $assistants,
        ];
    }

    private function getRepresentativesPerformance($weekStart, $weekEnd)
    {
        $representatives = User::where('role', 'representative')
            ->get()
            ->map(function ($rep) use ($weekStart, $weekEnd) {
                $totalAppointments = Appointment::where('representative_id', $rep->id)
                    ->whereBetween('created_at', [$weekStart, $weekEnd])
                    ->count();

                $purchases = Purchase::whereHas('appointment', function ($query) use ($rep, $weekStart, $weekEnd) {
                    $query->where('representative_id', $rep->id)
                        ->whereBetween('created_at', [$weekStart, $weekEnd]);
                })->get();

                $totalRevenue = $purchases->where('status', 'purchased')->sum('resale_price') ?? 0;
                $totalPurchases = $purchases->count();
                $conversionRate = $totalAppointments > 0
                    ? round(($totalPurchases / $totalAppointments) * 100, 1)
                    : 0;

                return [
                    'id' => $rep->id,
                    'name' => $rep->name,
                    'email' => $rep->email,
                    'total_purchases' => $totalPurchases,
                    'total_revenue' => $totalRevenue,
                    'total_appointments' => $totalAppointments,
                    'conversion_rate' => $conversionRate,
                    'avatar' => $this->generateInitials($rep->name),
                ];
            })
            ->sortByDesc('total_revenue')
            ->values();

        return [
            'total_representatives' => $representatives->count(),
            'total_purchases' => $representatives->sum('total_purchases'),
            'total_revenue' => $representatives->sum('total_revenue'),
            'avg_conversion_rate' => $representatives->avg('conversion_rate'),
            'leaderboard' => $representatives->take(5),
            'all_representatives' => $representatives,
        ];
    }

    private function getPairingInsights($weekStart, $weekEnd)
    {
        // Get appointments with both assistant and representative data
        $pairings = DB::table('appointments')
            ->join('users as assistants', 'appointments.assistant_id', '=', 'assistants.id')
            ->join('users as representatives', 'appointments.representative_id', '=', 'representatives.id')
            ->leftJoin('purchases', 'appointments.id', '=', 'purchases.appointment_id')
            ->select(
                'assistants.id as assistant_id',
                'assistants.name as assistant_name',
                'representatives.id as representative_id',
                'representatives.name as representative_name',
                'appointments.source',
                DB::raw('COUNT(DISTINCT appointments.id) as total_appointments'),
                DB::raw('COUNT(DISTINCT CASE WHEN purchases.status = "purchased" THEN appointments.id END) as successful_appointments')
            )
            ->whereBetween('appointments.created_at', [$weekStart, $weekEnd])
            ->groupBy('assistants.id', 'assistants.name', 'representatives.id', 'representatives.name', 'appointments.source')
            ->get()
            ->map(function ($pairing) {
                $successRate = $pairing->total_appointments > 0
                    ? round(($pairing->successful_appointments / $pairing->total_appointments) * 100, 1)
                    : 0;

                // Calculate collaboration score based on success rate and appointment volume
                $collaborationScore = min(100, ($successRate * 0.7) + (min($pairing->total_appointments, 20) * 1.5));

                return [
                    'assistant_id' => $pairing->assistant_id,
                    'assistant_name' => $pairing->assistant_name,
                    'representative_id' => $pairing->representative_id,
                    'representative_name' => $pairing->representative_name,
                    'source' => $pairing->source,
                    'total_appointments' => $pairing->total_appointments,
                    'successful_appointments' => $pairing->successful_appointments,
                    'success_rate' => $successRate,
                    'collaboration_score' => round($collaborationScore, 1),
                    'assistant_avatar' => $this->generateInitials($pairing->assistant_name),
                    'representative_avatar' => $this->generateInitials($pairing->representative_name),
                ];
            })
            ->sortByDesc('success_rate')
            ->values();

        return [
            'total_pairings' => $pairings->count(),
            'avg_success_rate' => $pairings->avg('success_rate'),
            'avg_collaboration_score' => $pairings->avg('collaboration_score'),
            'top_pair' => $pairings->first(),
            'all_pairings' => $pairings,
        ];
    }

    private function getPerformanceTrends()
    {
        $weeks = [];
        for ($i = 3; $i >= 0; $i--) {
            $weekStart = Carbon::now()->subWeeks($i)->startOfWeek();
            $weekEnd = Carbon::now()->subWeeks($i)->endOfWeek();

            $assistantAppointments = Appointment::whereBetween('created_at', [$weekStart, $weekEnd])->count();
            $repRevenue = Purchase::whereHas('appointment')->whereBetween('created_at', [$weekStart, $weekEnd])
                ->where('status', 'purchased')->sum('resale_price');
            $weeks[] = [
                'week' => $weekStart->format('M d'),
                'assistant_appointments' => $assistantAppointments,
                'rep_revenue' => $repRevenue,
            ];
        }

        return $weeks;
    }

    private function generateInitials($name)
    {
        $words = explode(' ', $name);
        $initials = '';
        foreach ($words as $word) {
            $initials .= strtoupper(substr($word, 0, 1));
        }
        return substr($initials, 0, 2);
    }

    private function extractCityFromAddress($address)
    {
        if (empty($address)) {
            return 'Non spécifiée';
        }

        // Common Moroccan cities to look for in addresses
        $cities = ['Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Tanger', 'Agadir', 'Meknès', 'Oujda', 'Kenitra', 'Tétouan'];

        foreach ($cities as $city) {
            if (stripos($address, $city) !== false) {
                return $city;
            }
        }

        // If no known city found, try to extract the last part of the address
        $parts = explode(',', $address);
        $lastPart = trim(end($parts));

        // Return the last part if it's not too long, otherwise return a generic region
        return strlen($lastPart) <= 20 ? $lastPart : 'Autre région';
    }

    private function getDetailedPerformanceMetrics($weekStart, $weekEnd)
    {
        return [
            'overall_performance' => 87,
            'conversion_rate' => 78.5,
            'avg_revenue_per_pair' => 45200,
            'team_efficiency' => 92,
            'trends' => [
                'performance' => 5.2,
                'conversion' => 3.1,
                'revenue' => -2.3,
                'efficiency' => 7.8
            ]
        ];
    }

    private function getTeamComparisonData($weekStart, $weekEnd)
    {
        return [
            ['name' => 'Équipe Nord', 'assistants' => 8, 'representatives' => 6, 'avgSuccess' => 78, 'totalRevenue' => 145000],
            ['name' => 'Équipe Sud', 'assistants' => 6, 'representatives' => 5, 'avgSuccess' => 82, 'totalRevenue' => 132000],
            ['name' => 'Équipe Est', 'assistants' => 7, 'representatives' => 4, 'avgSuccess' => 75, 'totalRevenue' => 98000],
            ['name' => 'Équipe Ouest', 'assistants' => 5, 'representatives' => 6, 'avgSuccess' => 85, 'totalRevenue' => 156000]
        ];
    }

    private function getHistoricalPerformance()
    {
        $weeks = [];
        for ($i = 7; $i >= 0; $i--) {
            $weekStart = Carbon::now()->subWeeks($i)->startOfWeek();
            $weekEnd = Carbon::now()->subWeeks($i)->endOfWeek();

            $assistantCount = Appointment::whereBetween('created_at', [$weekStart, $weekEnd])->count();
            $repCount = Purchase::whereHas('appointment', function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('created_at', [$weekStart, $weekEnd]);
            })->count();
            $revenue = Purchase::whereHas('appointment', function ($query) use ($weekStart, $weekEnd) {
                $query->whereBetween('created_at', [$weekStart, $weekEnd]);
            })->sum('resale_price');
            $appointments = Appointment::whereBetween('created_at', [$weekStart, $weekEnd])->count();

            $weeks[] = [
                'week' => 'S' . (8 - $i),
                'assistants' => $assistantCount,
                'representatives' => $repCount,
                'revenue' => $revenue,
                'appointments' => $appointments,
            ];
        }

        return $weeks;
    }

    private function getCurrentPairings($weekStart, $weekEnd)
    {
        return $this->getPairingInsights($weekStart, $weekEnd)['all_pairings'];
    }

    private function getPairingEffectivenessAnalysis()
    {
        // Get real pairing effectiveness data
        $now = Carbon::now();
        $weekStart = $now->copy()->startOfWeek()->format('Y-m-d H:i:s');
        $weekEnd = $now->copy()->endOfWeek()->format('Y-m-d H:i:s');

        $pairings = $this->getPairingInsights($weekStart, $weekEnd)['all_pairings'];

        $avgCollaborationScore = $pairings->avg('collaboration_score') ?? 0;
        $avgSuccessRate = $pairings->avg('success_rate') ?? 0;
        $totalPairings = $pairings->count();

        // Identify high-performing sources
        $sourcePerformance = $pairings->groupBy('source')->map(function ($group) {
            return $group->avg('success_rate');
        })->sortDesc();

        return [
            'avg_collaboration_score' => round($avgCollaborationScore, 1),
            'avg_success_rate' => round($avgSuccessRate, 1),
            'total_active_pairings' => $totalPairings,
            'best_performing_source' => $sourcePerformance->keys()->first() ?? 'N/A',
            'improvement_opportunities' => $pairings->where('success_rate', '<', 70)->count(),
            'high_performers' => $pairings->where('success_rate', '>=', 80)->count()
        ];
    }

    private function getRegionalPerformance($weekStart, $weekEnd)
    {
        // Check if we're using SQLite or MySQL for proper string concatenation
        $isSqlite = DB::getDriverName() === 'sqlite';

        // Use appropriate string concatenation based on database driver
        $pairingCountExpression = $isSqlite
            ? DB::raw('COUNT(DISTINCT (appointments.assistant_id || "-" || appointments.representative_id)) as pairings')
            : DB::raw('COUNT(DISTINCT CONCAT(appointments.assistant_id, "-", appointments.representative_id)) as pairings');

        // Get regional data based on client addresses
        $regionalData = DB::table('appointments')
            ->leftJoin('purchases', 'appointments.id', '=', 'purchases.appointment_id')
            ->select(
                'appointments.clientAddress',
                $pairingCountExpression,
                DB::raw('COUNT(DISTINCT appointments.id) as total_appointments'),
                DB::raw('COUNT(DISTINCT CASE WHEN purchases.status = "purchased" THEN appointments.id END) as successful_appointments'),
                DB::raw('COALESCE(SUM(purchases.resale_price), 0) as total_revenue')
            )
            ->whereBetween('appointments.created_at', [$weekStart, $weekEnd])
            ->groupBy('appointments.clientAddress')
            ->get()
            ->map(function ($item) {
                $region = $this->extractCityFromAddress($item->clientAddress);
                $avgSuccess = $item->total_appointments > 0
                    ? round(($item->successful_appointments / $item->total_appointments) * 100, 1)
                    : 0;

                return [
                    'region' => $region,
                    'pairings' => $item->pairings,
                    'avgSuccess' => $avgSuccess,
                    'totalRevenue' => $item->total_revenue
                ];
            })
            ->groupBy('region')
            ->map(function ($regionGroup, $regionName) {
                return [
                    'region' => $regionName,
                    'pairings' => $regionGroup->sum('pairings'),
                    'avgSuccess' => $regionGroup->avg('avgSuccess'),
                    'totalRevenue' => $regionGroup->sum('totalRevenue')
                ];
            })
            ->values()
            ->toArray();

        return $regionalData;
    }
}
