import { Head, Link, usePage } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import AppLogoIcon from '@/components/app-logo-icon';
import {
    Users,
    Calendar,
    DollarSign,
    TrendingUp,
    Shield,
    Award,
    LayoutDashboard,
    LogIn,
    ArrowRight
} from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage().props;

    const features = [
        {
            icon: LayoutDashboard,
            title: "Tableau de Bord Global",
            description: "Vue d'ensemble des performances et statistiques en temps réel"
        },
        {
            icon: Users,
            title: "Système de Pairing",
            description: "Gestion intelligente des équipes SA/SR pour optimiser les résultats"
        },
        {
            icon: Calendar,
            title: "Gestion des RDV",
            description: "Base de données complète pour le suivi des rendez-vous clients"
        },
        {
            icon: Award,
            title: "Système de Bonus",
            description: "Calcul automatique et suivi des bonus de performance"
        },
        {
            icon: TrendingUp,
            title: "Gestion du Podium",
            description: "Classements et performances des équipes commerciales"
        },
        {
            icon: Shield,
            title: "Gestion Utilisateurs",
            description: "Administration complète des comptes et permissions"
        }
    ];

    return (
        <>
            <Head title="Bienvenue">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            <div className="min-h-screen bg-background">
                {/* Header */}
                <header className="border-b border-border bg-white/50 backdrop-blur-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            {/* Logo */}
                            <div className="flex items-center space-x-3">
                                <div className="bg-primary text-primary-foreground flex aspect-square size-10 items-center justify-center rounded-md">
                                    <AppLogoIcon className="size-6 fill-current text-white" />
                                </div>
                                <div className="text-left">
                                    <h1 className="text-lg font-semibold text-foreground">AS Shop</h1>
                                    <p className="text-xs text-muted-foreground">Back-Office Platform</p>
                                </div>
                            </div>

                            {/* Navigation */}
                            <nav className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>
                                            <LayoutDashboard className="mr-2 h-4 w-4" />
                                            Tableau de Bord
                                        </Link>
                                    </Button>
                                ) : (
                                    <Button asChild>
                                        <Link href={route('login')}>
                                            <LogIn className="mr-2 h-4 w-4" />
                                            Se Connecter
                                        </Link>
                                    </Button>
                                )}
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Main Content */}
                <main className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
                    {/* Hero Section */}
                    <div className="text-center">
                        <div className="mx-auto mb-8 flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
                            <AppLogoIcon className="h-10 w-10 fill-current text-primary" />
                        </div>
                        <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                            Bienvenue sur AS Shop
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-muted-foreground">
                            Plateforme de gestion back-office pour optimiser vos performances commerciales.
                            Gérez vos équipes, suivez vos résultats et maximisez votre chiffre d'affaires.
                        </p>
                        <div className="mt-10 flex items-center justify-center gap-x-6">
                            {auth.user ? (
                                <Button size="lg" asChild>
                                    <Link href={route('dashboard')}>
                                        <LayoutDashboard className="mr-2 h-5 w-5" />
                                        Accéder au Tableau de Bord
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            ) : (
                                <Button size="lg" asChild>
                                    <Link href={route('login')}>
                                        <LogIn className="mr-2 h-5 w-5" />
                                        Se Connecter
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            )}
                        </div>
                    </div>

                    {/* Features Grid */}
                    <div className="mt-24">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                                Fonctionnalités Principales
                            </h2>
                            <p className="mx-auto mt-4 max-w-2xl text-lg text-muted-foreground">
                                Découvrez tous les outils disponibles pour optimiser votre gestion commerciale
                            </p>
                        </div>

                        <div className="mt-16 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                            {features.map((feature, index) => (
                                <Card key={index} className="border-0 bg-white shadow-sm hover:shadow-md transition-shadow">
                                    <CardHeader className="pb-4">
                                        <div className="flex items-center space-x-4">
                                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                                <feature.icon className="h-6 w-6 text-primary" />
                                            </div>
                                            <CardTitle className="text-lg font-semibold text-foreground">
                                                {feature.title}
                                            </CardTitle>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                            {feature.description}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>

                    {/* Stats Section */}
                    <div className="mt-24 bg-muted/30 py-16">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                                    Optimisez Vos Performances
                                </h2>
                                <p className="mx-auto mt-4 max-w-2xl text-lg text-muted-foreground">
                                    Des outils puissants pour maximiser votre efficacité commerciale
                                </p>
                            </div>

                            <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                                <div className="text-center">
                                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                                        <DollarSign className="h-8 w-8 text-green-600" />
                                    </div>
                                    <h3 className="mt-4 text-lg font-semibold text-foreground">Suivi CA</h3>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Monitoring en temps réel de votre chiffre d'affaires
                                    </p>
                                </div>

                                <div className="text-center">
                                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                                        <Users className="h-8 w-8 text-blue-600" />
                                    </div>
                                    <h3 className="mt-4 text-lg font-semibold text-foreground">Équipes</h3>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Gestion optimisée des binômes SA/SR
                                    </p>
                                </div>

                                <div className="text-center">
                                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
                                        <Calendar className="h-8 w-8 text-purple-600" />
                                    </div>
                                    <h3 className="mt-4 text-lg font-semibold text-foreground">RDV</h3>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Planification et suivi des rendez-vous
                                    </p>
                                </div>

                                <div className="text-center">
                                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-orange-100">
                                        <Award className="h-8 w-8 text-orange-600" />
                                    </div>
                                    <h3 className="mt-4 text-lg font-semibold text-foreground">Bonus</h3>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Système de récompenses automatisé
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                {/* Footer */}
                <footer className="border-t border-border bg-white/50 backdrop-blur-sm">
                    <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
                        <div className="flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0">
                            <div className="flex items-center space-x-3">
                                <div className="bg-primary text-primary-foreground flex aspect-square size-8 items-center justify-center rounded-md">
                                    <AppLogoIcon className="size-5 fill-current text-white" />
                                </div>
                                <div className="text-left">
                                    <p className="text-sm font-semibold text-foreground">AS Shop</p>
                                    <p className="text-xs text-muted-foreground">Back-Office Platform</p>
                                </div>
                            </div>
                            <p className="text-sm text-muted-foreground">
                                © 2024 AS Shop. Tous droits réservés.
                            </p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}