<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::insert([
            [
                'name' => '<PERSON><PERSON> Papa',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            // Assistants
            [
                'name' => 'Yahya SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Amina SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Karim SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Fatima SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Omar SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Leila SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Youssef SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Nadia SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Mehdi SA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'assistant',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            // Representatives
            [
                'name' => 'Hamza SR',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'representative',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Ousama SR',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'representative',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Rachid SR',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'representative',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Salma SR',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'representative',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            // Other roles
            [
                'name' => 'Sara HR',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'recruiter',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ],
            [
                'name' => 'Nassima EA',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'executive',
                'is_activated' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
            ]
        ]);
    }
}
