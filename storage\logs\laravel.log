[2025-08-04 17:39:51] production.ERROR: file_get_contents(C:\Users\<USER>\Desktop\AS-shop\.env): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_get_contents(C:\\Users\\<USER>\\Desktop\\AS-shop\\.env): Failed to open stream: No such file or directory at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php:100)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php(100): file_get_contents()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php(82): Illuminate\\Foundation\\Console\\KeyGenerateCommand->writeNewEnvironmentFileWith()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\KeyGenerateCommand.php(47): Illuminate\\Foundation\\Console\\KeyGenerateCommand->setKeyInEnvironmentFile()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\KeyGenerateCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
[2025-08-04 18:04:54] local.INFO: Save assignments request received {"data":{"assignments":[{"assistant_id":2,"assistant_name":"Yahya SA","representative_ids":[11,12]},{"assistant_id":3,"assistant_name":"Amina SA","representative_ids":[11,13]},{"assistant_id":4,"assistant_name":"Karim SA","representative_ids":[14]},{"assistant_id":5,"assistant_name":"Fatima SA","representative_ids":[]},{"assistant_id":6,"assistant_name":"Omar SA","representative_ids":[]},{"assistant_id":7,"assistant_name":"Leila SA","representative_ids":[]},{"assistant_id":8,"assistant_name":"Youssef SA","representative_ids":[]},{"assistant_id":9,"assistant_name":"Nadia SA","representative_ids":[]},{"assistant_id":10,"assistant_name":"Mehdi SA","representative_ids":[]}]}} 
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 2 {"representative_ids":[11,12]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 2  
[2025-08-04 18:04:54] local.INFO: Created assignment {"id":1,"assistant_id":2,"representative_id":11} 
[2025-08-04 18:04:54] local.INFO: Created assignment {"id":2,"assistant_id":2,"representative_id":12} 
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 3 {"representative_ids":[11,13]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 3  
[2025-08-04 18:04:54] local.INFO: Created assignment {"id":3,"assistant_id":3,"representative_id":11} 
[2025-08-04 18:04:54] local.INFO: Created assignment {"id":4,"assistant_id":3,"representative_id":13} 
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 4 {"representative_ids":[14]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 4  
[2025-08-04 18:04:54] local.INFO: Created assignment {"id":5,"assistant_id":4,"representative_id":14} 
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 5 {"representative_ids":[]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 5  
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 6 {"representative_ids":[]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 6  
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 7 {"representative_ids":[]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 7  
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 8 {"representative_ids":[]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 8  
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 9 {"representative_ids":[]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 9  
[2025-08-04 18:04:54] local.INFO: Processing assignment for assistant 10 {"representative_ids":[]} 
[2025-08-04 18:04:54] local.INFO: Deleted 0 existing assignments for assistant 10  
[2025-08-04 18:04:54] local.INFO: Assignments saved successfully  
[2025-08-04 18:46:53] local.ERROR: The process "C:\php-8.4.7\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1" exceeded the timeout of 60 seconds. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\ProcessTimedOutException(code: 0): The process \"C:\\php-8.4.7\\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1\" exceeded the timeout of 60 seconds. at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\process\\Process.php:1181)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\process\\Process.php(450): Symfony\\Component\\Process\\Process->checkTimeout()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\process\\Process.php(251): Symfony\\Component\\Process\\Process->wait()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(181): Symfony\\Component\\Process\\Process->run()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(92): Illuminate\\Queue\\Listener->runProcess()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListenCommand.php(75): Illuminate\\Queue\\Listener->listen()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListenCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
[2025-08-14 22:49:12] testing.ERROR: Call to undefined method App\Models\User::createToken() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\User::createToken() at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Controllers\\Api\\AuthController.php(47): Illuminate\\Database\\Eloquent\\Model->__call()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\AuthController->login()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\tests\\Feature\\Api\\AuthApiTest.php(23): Illuminate\\Foundation\\Testing\\TestCase->postJson()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): Tests\\Feature\\Api\\AuthApiTest->test_user_can_login_with_valid_credentials()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run()
#50 {main}
"} 
[2025-08-14 22:49:49] testing.ERROR: Call to a member function toISOString() on string {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function toISOString() on string at C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Resources\\AppointmentResource.php:25)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): App\\Http\\Resources\\AppointmentResource->toArray()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(255): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(953): Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#3 [internal function]: Illuminate\\Support\\Collection->{closure:Illuminate\\Support\\Traits\\EnumeratesValues::jsonSerialize():951}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(951): array_map()
#5 [internal function]: Illuminate\\Support\\Collection->jsonSerialize()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(86): json_encode()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\http-foundation\\JsonResponse.php(49): Illuminate\\Http\\JsonResponse->setData()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(105): Illuminate\\Http\\JsonResponse->__construct()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php(17): Illuminate\\Routing\\ResponseFactory->json()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(134): Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(114): Illuminate\\Http\\Resources\\Json\\ResourceCollection->preparePaginatedResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(232): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Traits\\ApiResponse.php(113): Illuminate\\Http\\Resources\\Json\\JsonResource->response()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Controllers\\Api\\AppointmentController.php(67): App\\Http\\Controllers\\Api\\AppointmentController->paginatedResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\AppointmentController->index()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(381): Illuminate\\Foundation\\Testing\\TestCase->json()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\tests\\Feature\\Api\\AppointmentApiTest.php(41): Illuminate\\Foundation\\Testing\\TestCase->getJson()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): Tests\\Feature\\Api\\AppointmentApiTest->test_can_get_appointments_list()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run()
#65 {main}
"} 
[2025-08-14 22:49:49] testing.ERROR: Call to a member function toISOString() on string {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function toISOString() on string at C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Resources\\AppointmentResource.php:25)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): App\\Http\\Resources\\AppointmentResource->toArray()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(39): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(245): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(232): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Traits\\ApiResponse.php(23): Illuminate\\Http\\Resources\\Json\\JsonResource->response()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Traits\\ApiResponse.php(93): App\\Http\\Controllers\\Api\\AppointmentController->successResponse()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Controllers\\Api\\AppointmentController.php(122): App\\Http\\Controllers\\Api\\AppointmentController->createdResponse()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\AppointmentController->store()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\tests\\Feature\\Api\\AppointmentApiTest.php(82): Illuminate\\Foundation\\Testing\\TestCase->postJson()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): Tests\\Feature\\Api\\AppointmentApiTest->test_can_create_appointment()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run()
#56 {main}
"} 
[2025-08-14 22:49:49] testing.ERROR: Call to a member function toISOString() on string {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function toISOString() on string at C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Resources\\AppointmentResource.php:25)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): App\\Http\\Resources\\AppointmentResource->toArray()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(39): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(245): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(232): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Traits\\ApiResponse.php(23): Illuminate\\Http\\Resources\\Json\\JsonResource->response()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Controllers\\Api\\AppointmentController.php(135): App\\Http\\Controllers\\Api\\AppointmentController->successResponse()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\AppointmentController->show()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(381): Illuminate\\Foundation\\Testing\\TestCase->json()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\tests\\Feature\\Api\\AppointmentApiTest.php(120): Illuminate\\Foundation\\Testing\\TestCase->getJson()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): Tests\\Feature\\Api\\AppointmentApiTest->test_can_get_single_appointment()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run()
#55 {main}
"} 
[2025-08-14 22:49:49] testing.ERROR: Call to a member function toISOString() on string {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function toISOString() on string at C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Resources\\AppointmentResource.php:25)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): App\\Http\\Resources\\AppointmentResource->toArray()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(39): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(245): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(232): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Traits\\ApiResponse.php(23): Illuminate\\Http\\Resources\\Json\\JsonResource->response()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Controllers\\Api\\AppointmentController.php(202): App\\Http\\Controllers\\Api\\AppointmentController->successResponse()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\AppointmentController->update()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(441): Illuminate\\Foundation\\Testing\\TestCase->json()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\tests\\Feature\\Api\\AppointmentApiTest.php(158): Illuminate\\Foundation\\Testing\\TestCase->putJson()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): Tests\\Feature\\Api\\AppointmentApiTest->test_can_update_appointment()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run()
#55 {main}
"} 
[2025-08-14 22:49:49] testing.ERROR: Call to a member function toISOString() on string {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function toISOString() on string at C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Resources\\AppointmentResource.php:25)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): App\\Http\\Resources\\AppointmentResource->toArray()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(255): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(953): Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#3 [internal function]: Illuminate\\Support\\Collection->{closure:Illuminate\\Support\\Traits\\EnumeratesValues::jsonSerialize():951}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(951): array_map()
#5 [internal function]: Illuminate\\Support\\Collection->jsonSerialize()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(86): json_encode()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\http-foundation\\JsonResponse.php(49): Illuminate\\Http\\JsonResponse->setData()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(105): Illuminate\\Http\\JsonResponse->__construct()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php(17): Illuminate\\Routing\\ResponseFactory->json()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(134): Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(114): Illuminate\\Http\\Resources\\Json\\ResourceCollection->preparePaginatedResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(232): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Traits\\ApiResponse.php(113): Illuminate\\Http\\Resources\\Json\\JsonResource->response()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Controllers\\Api\\AppointmentController.php(239): App\\Http\\Controllers\\Api\\AppointmentController->paginatedResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\AppointmentController->myAppointments()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(381): Illuminate\\Foundation\\Testing\\TestCase->json()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\tests\\Feature\\Api\\AppointmentApiTest.php(212): Illuminate\\Foundation\\Testing\\TestCase->getJson()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): Tests\\Feature\\Api\\AppointmentApiTest->test_can_get_my_appointments()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run()
#65 {main}
"} 
[2025-08-15 18:05:33] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-16 17:02:13] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-17 12:22:06] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-17 22:16:29] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-18 18:24:41] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-19 16:52:33] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-19 16:52:43] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-26 14:37:49] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Desktop\AS-shop\public\build/manifest.json (View: C:\Users\<USER>\Desktop\AS-shop\resources\views\app.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Desktop\\AS-shop\\resources\\views\\app.blade.php) at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\build/manifest.json at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest()
#1 C:\\Users\\<USER>\\Desktop\\AS-shop\\storage\\framework\\views\\a16a49224bb243cb87fadb6e7ad093e6.php(41): Illuminate\\Foundation\\Vite->__invoke()
#2 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make()
#13 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(128): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Inertia\\Response->toResponse()
#16 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#20 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#21 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#22 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\AS-shop\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#24 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\HandleAppearance->handle()
#25 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#26 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#28 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#30 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#32 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#35 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#37 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#39 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#45 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()
#46 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#49 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#52 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#54 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#56 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#58 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#60 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#62 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}()
#64 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\AS-shop\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#68 C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-08-26 14:37:53] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\AS-shop\\vendor\\symfony\\error-handler\\Resources\\views\\trace.html.php:1)
[stacktrace]
#0 {main}
"} 
